.dragable-directories .drag-area {
  @apply my-1 p-is-3 m-is-3;
  border-inline-start: 1px solid var(--color-border, rgba(255, 255, 255, 0.12));
}

/* 拖拽状态样式 */
.dragable-directories .ghost {
  opacity: 0.5;
  transition: opacity 0.2s ease-in-out;
}

/* 选中状态样式 */
.dragable-directories .nav-directory-item.selected {
  background-color: rgba(101, 111, 172, 0.1);
  border-left: 3px solid var(--color-accent, #656fac);
  padding-left: 12px;
  transition: all 0.2s ease-in-out;
}

/* 暗色模式下的选中状态 */
.dark .dragable-directories .nav-directory-item.selected {
  background-color: rgba(101, 111, 172, 0.2);
}

/* 目录项样式 */
.dragable-directories .directory-item {
  margin-bottom: 2px;
  color: var(--color-text-primary, #e5e7eb);
}

/* 目录头部样式 */
.dragable-directories .directory-header {
  user-select: none;
  border-radius: 6px;
  transition: all 0.2s ease-in-out;
  color: var(--color-text-primary, #e5e7eb);
}

.dragable-directories .directory-header:hover {
  background-color: var(--color-bg-secondary, rgba(0, 0, 0, 0.05));
  transform: translateX(2px);
}

.dark .dragable-directories .directory-header:hover {
  background-color: var(--color-bg-secondary, rgba(255, 255, 255, 0.05));
}

/* 展开箭头样式 */
.dragable-directories .expand-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--color-text-secondary, #9ca3af);
}

.dragable-directories .expand-arrow:hover {
  background-color: var(--color-bg-hover, rgba(0, 0, 0, 0.1));
}

.dark .dragable-directories .expand-arrow:hover {
  background-color: var(--color-bg-hover, rgba(255, 255, 255, 0.1));
}

/* 子目录容器样式 */
.dragable-directories .children-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
}

/* 展开状态的子目录 */
.dragable-directories .children-container.expanded {
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 折叠状态的子目录 */
.dragable-directories .children-container.collapsed {
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 滑动动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 1000px;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 1;
    max-height: 1000px;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
}

/* 文件夹图标动画 */
.dragable-directories .directory-header .i-material-symbols-folder-outline,
.dragable-directories
  .directory-header
  .i-material-symbols-folder-open-outline {
  transition: all 0.2s ease-in-out;
  color: var(--color-icon, #9ca3af);
}

/* 暗色模式下的拖拽区域 */
.dark .dragable-directories .drag-area {
  background-color: var(--color-bg-tertiary, rgba(255, 255, 255, 0.02));
  border-color: var(--color-border, rgba(255, 255, 255, 0.1));
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dragable-directories .directory-header {
    padding: 6px 8px;
  }

  .dragable-directories .expand-arrow {
    width: 18px;
    height: 18px;
  }

  .dragable-directories .nav-directory-item.selected {
    padding-left: 10px;
  }
}

/* 紧凑模式 - 用于空间受限的场景 */
.dragable-directories.compact .directory-header {
  padding: 4px 6px;
  font-size: 0.875rem;
}

.dragable-directories.compact .expand-arrow {
  width: 16px;
  height: 16px;
}

.dragable-directories.compact .nav-directory-item.selected {
  padding-left: 8px;
}

/* 移动端紧凑模式调整 */
@media (max-width: 768px) {
  .dragable-directories.compact .directory-header {
    padding: 3px 5px;
    font-size: 0.8rem;
  }

  .dragable-directories.compact .expand-arrow {
    width: 14px;
    height: 14px;
  }
}
