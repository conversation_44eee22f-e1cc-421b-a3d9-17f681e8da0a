/* rss-navigation 组件样式 - Obsidian 风格 */
.rss-navigation {
  @apply p-6  box-border;
  background-color: #27282e;
  border-radius: 10px;
  width: 100%;
  max-height: 100%;
  overflow-y: auto;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.rss-navigation::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.rss-navigation .nav-header {
  @apply flex justify-start items-center mb-3 border-b border-gray-700;
}

.rss-navigation .nav-header-icon-btn {
  @apply text-gray-400 cursor-pointer rounded-lg w-8 h-8 ml-1 flex items-center justify-center;
}

.rss-navigation .nav-header-icon-btn:hover {
  background-color: #33353f;
}

.rss-navigation .nav-header-btn {
  @apply flex items-center justify-center px-3 py-1 rounded-lg cursor-pointer text-gray-200 text-sm font-medium;
  background-color: #656fac;
  width: fit-content;
  box-shadow: 1px 1px 1px #565e92;
}

.rss-navigation .nav-header-btn:hover {
  background-color: #909cd0;
}

.rss-navigation .nav-header-icon-btn {
}

.rss-navigation .nav-header h2 {
  @apply m-0 mb-2 text-gray-100 text-lg font-medium;
  font-weight: 500;
}

.rss-navigation .subtitle {
  @apply m-0 text-gray-400 text-xs uppercase tracking-wide;
}

.rss-navigation .nav-links {
  @apply flex flex-col gap-1 mb-8;
}

.rss-navigation .nav-link {
  @apply flex items-center px-3 py-2 no-underline text-gray-300 rounded-md transition-all duration-200;
  font-size: 14px;
  font-weight: 400;
}

.rss-navigation .nav-link:hover {
  @apply bg-gray-700 text-gray-100;
}

.rss-navigation .nav-link.active {
  @apply bg-gray-700 text-white;
  background-color: rgba(99, 102, 241, 0.1);
  border-left: 3px solid #6366f1;
  padding-left: 12px;
}

.rss-navigation .nav-icon {
  @apply text-base mr-3 w-4 text-center;
  opacity: 0.8;
}

.rss-navigation .nav-text {
  @apply flex-1 font-normal;
}

.rss-navigation .nav-code {
  @apply text-xs opacity-50 bg-gray-600 px-1.5 py-0.5 rounded font-mono;
  font-size: 10px;
}

.rss-navigation .nav-link.active .nav-code {
  @apply bg-indigo-600 bg-opacity-50 text-indigo-200;
}

.rss-navigation .nav-info {
  @apply p-4 bg-gray-750 rounded-lg text-sm border border-gray-600;
  background-color: rgba(55, 65, 81, 0.5);
}

.rss-navigation .nav-info h3 {
  @apply text-gray-200 text-xs font-medium mb-3 uppercase tracking-wide;
}

.rss-navigation .nav-info .space-y-2 > div {
  @apply flex justify-between items-center py-1;
}

.rss-navigation .nav-info span {
  @apply text-gray-400 text-xs;
}

.rss-navigation .nav-info code {
  @apply bg-gray-600 text-gray-200 px-2 py-1 rounded text-xs font-mono;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rss-navigation {
    @apply w-full min-h-auto;
  }

  .rss-navigation .nav-links {
    @apply flex-row flex-wrap gap-2;
  }

  .rss-navigation .nav-link {
    @apply flex-1 min-w-32 justify-center text-center;
  }

  .rss-navigation .nav-text {
    @apply flex-none;
  }

  .rss-navigation .nav-code {
    @apply hidden;
  }

  .rss-navigation .nav-info {
    @apply mt-4;
  }
}
