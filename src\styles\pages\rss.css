/* RSS 页面样式 */

.rss-page {
  @apply p-6 max-w-4xl mx-auto;
}

.rss-header {
  @apply text-center mb-8;
}

.rss-header h1 {
  @apply text-3xl font-bold mb-2;
}

.rss-header p {
  @apply text-lg;
}

/* RSS 输入区域 */
.rss-input-section {
  @apply p-6 my-6;
}

.rss-input-section h3 {
  @apply text-xl font-semibold mb-4;
}

.input-group {
  @apply flex gap-3;
}

.rss-input {
  @apply flex-1 px-4 py-3 rounded-lg border-2 border-solid;
  background-color: var(--color-bg-primary);
  border-color: var(--color-border);
  color: var(--color-text-primary);
  font-size: 14px;
}

.rss-input:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.rss-input::placeholder {
  color: var(--color-text-secondary);
}

.subscribe-btn {
  @apply px-6 py-3 rounded-lg font-medium transition-all duration-200;
  background-color: var(--color-accent);
  color: white;
  border: none;
  cursor: pointer;
  min-width: 100px;
}

.subscribe-btn:hover:not(:disabled) {
  background-color: #5856eb;
  transform: translateY(-1px);
}

.subscribe-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 加载状态 */
.loading {
  @apply p-8 text-center;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-solid rounded-full mx-auto mb-4;
  border-color: var(--color-accent) transparent var(--color-accent) transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 错误状态 */
.error {
  @apply p-6 mb-6;
  border-left: 4px solid #ef4444;
}

.error-title {
  @apply text-lg font-semibold mb-2;
  color: #ef4444;
}

.error-message {
  @apply mb-4;
  color: var(--color-text-secondary);
}

.clear-error-btn {
  @apply px-4 py-2 rounded-lg;
  background-color: #ef4444;
  color: white;
  border: none;
  cursor: pointer;
}

.clear-error-btn:hover {
  background-color: #dc2626;
}

/* RSS 频道信息 */
.rss-channel {
  @apply p-6 mb-6;
}

.channel-title {
  @apply text-2xl font-bold mb-3;
}

.channel-description {
  @apply text-base mb-4 leading-relaxed;
}

.channel-meta {
  @apply flex items-center gap-4;
}

.item-count {
  @apply px-3 py-1 rounded-full text-sm;
  background-color: var(--color-accent);
  color: white;
}

/* RSS 文章列表 */
.rss-items {
  @apply space-y-4;
}

.rss-item {
  @apply p-6 transition-all duration-200;
}

.rss-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.item-title {
  @apply text-lg font-semibold mb-3;
}

.item-link {
  @apply no-underline transition-colors duration-200;
}

.item-link:hover {
  color: var(--color-accent);
}

.item-description {
  @apply text-sm leading-relaxed mb-4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  @apply flex items-center justify-between;
}

.item-date {
  @apply text-sm;
}

.item-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-btn,
.copy-link-btn {
  @apply px-3 py-1 rounded text-sm transition-all duration-200;
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  cursor: pointer;
  text-decoration: none;
}

.action-btn:hover,
.copy-link-btn:hover {
  background-color: var(--color-accent);
  color: white;
  border-color: var(--color-accent);
  transform: translateY(-1px);
}

/* 示例 RSS 源 */
.rss-examples {
  @apply p-6;
}

.rss-examples h3 {
  @apply text-xl font-semibold mb-3;
}

.rss-examples p {
  @apply mb-4;
}

.example-list {
  @apply space-y-3;
}

.example-item {
  @apply flex items-center justify-between p-3 rounded-lg;
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
}

.example-info {
  @apply flex flex-col;
}

.example-info strong {
  @apply text-sm font-medium mb-1;
}

.example-url {
  @apply text-xs font-mono;
}

.use-example-btn {
  @apply px-4 py-2 rounded text-sm transition-all duration-200;
  background-color: var(--color-accent);
  color: white;
  border: none;
  cursor: pointer;
}

.use-example-btn:hover {
  background-color: #5856eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-group {
    @apply flex-col;
  }

  .rss-input {
    @apply mb-3;
  }

  .item-meta {
    @apply flex-col items-start gap-2;
  }

  .example-item {
    @apply flex-col items-start gap-3;
  }
}
