.input-tag-model {
  width: 100%;
  max-width: 500px;
  padding: 16px 0;
}

.model-description {
  margin-bottom: 16px;
  color: #9ca3af;
  font-size: 0.9rem;
}

.model-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 20px;
}

/* 按钮样式 - 与 ConfirmModal 完全一致 */
.btn-cancel,
.btn-confirm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border-radius: 6px;
  border: 1.5px solid;
  min-width: 80px;
  font-family: inherit;
}

.btn-cancel {
  color: #e4e4e7;
  border-color: #464647;
  background-color: #434450;
}

.btn-cancel:hover {
  color: #e4e4e7;
  border-color: #464647;
  background-color: #4a4b58;
  transform: translateY(-1px);
  box-shadow: inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.1),
    0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn-cancel:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.btn-confirm {
  color: white;
  border-color: #6366f1;
  background-color: #6366f1;
  box-shadow: inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.1);
}

.btn-confirm:hover {
  border-color: #818cf8;
  background-color: #5855eb;
  transform: translateY(-1px);
  box-shadow: inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.2),
    0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-confirm:active {
  transform: translateY(0);
  background-color: #4f46e5;
  box-shadow: inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.2),
    0 2px 6px rgba(99, 102, 241, 0.3);
}

/* 焦点状态 */
.btn-cancel:focus,
.btn-confirm:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2), 0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn-confirm:focus {
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.4),
    inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.2),
    0 4px 12px rgba(99, 102, 241, 0.3);
}

/* 禁用状态 */
.btn-cancel:disabled,
.btn-confirm:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  transform: none !important;
  box-shadow: none !important;
}
