<!-- src/components/Models/InputTagModel/InputTagModel.vue -->
<template>
  <SimpleModal :show="show" title="添加标签" @close="handleClose">
    <div class="input-tag-model">
      <div class="model-description">为文章添加自定义标签</div>
      <InputTag
        :key="articleId + show.toString()"
        v-model="selectedTags"
        :options="tagOptions"
        :max-tags="10"
        placeholder="输入标签名称..."
        @change="handleTagsChange"
      />

      <div class="model-actions">
        <button @click="handleSave" class="btn-confirm" :disabled="!hasChanges">
          保存标签
        </button>
        <button @click="handleClose" class="btn-cancel">取消</button>
      </div>
    </div>
  </SimpleModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import SimpleModal from "../SimpleModal/SimpleModal.vue";
import InputTag from "../../InputTag/InputTag.vue";
import { useRSSStore } from "@/stores/rssStore";
import { TagType } from "@/types/rss/rss-content";

interface Props {
  show: boolean;
  articleId: string;
  existingTags?: string[];
}

interface Emits {
  (e: "close"): void;
  (e: "save", tags: string[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  articleId: "",
  existingTags: () => [],
});

const emit = defineEmits<Emits>();

const rssStore = useRSSStore();

// 响应式数据
const selectedTags = ref<string[]>([]);
const tagOptions = ref<string[]>([]);

// 计算属性
const hasChanges = computed(() => {
  // 如果有选中的标签，就允许提交
  if (selectedTags.value.length > 0) {
    return true;
  }

  // 检查是否有新增的标签
  for (const tag of selectedTags.value) {
    if (!props.existingTags.includes(tag)) {
      return true;
    }
  }

  // 检查是否有删除的标签
  for (const tag of props.existingTags) {
    if (!selectedTags.value.includes(tag)) {
      return true;
    }
  }

  return false;
});

// 监听器
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      // 当模态框打开时，初始化选中的标签
      selectedTags.value = [...props.existingTags];

      tagOptions.value = rssStore.getAllTags(TagType.CUSTOM);
    }
  },
  { immediate: true }
);

// 方法
const handleTagsChange = (tags: string[]) => {
  selectedTags.value = tags;
};

const handleSave = async () => {
  try {
    // 为每个选中的标签添加到文章
    for (const tag of selectedTags.value) {
      await rssStore.toggleTag(props.articleId, TagType.CUSTOM, tag);
    }

    // 如果有现有标签但不再选中，则移除这些标签
    for (const existingTag of props.existingTags) {
      if (!selectedTags.value.includes(existingTag)) {
        await rssStore.toggleTag(props.articleId, TagType.CUSTOM, existingTag);
      }
    }

    emit("save", selectedTags.value);
    emit("close");
  } catch (error) {
    console.error("保存标签失败:", error);
    // 可以在这里添加错误提示
  }
};

const handleClose = () => {
  emit("close");
};

watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      // 当模态框打开时，初始化选中的标签
      selectedTags.value = [...props.existingTags];
    } else {
      // 当模态框关闭时，清空选中的标签
      selectedTags.value = [];
    }
  },
  { immediate: true }
);
</script>

<style scoped src="./InputTagModel.css"></style>
