<template>
  <div class="directory-navigation">
    <div class="nav-header">
      <h2 class="nav-title">文章目录</h2>
    </div>

    <div v-if="paragraphs.length > 0" class="paragraphs-container">
      <DragableDirectories
        ref="dragableDirectoriesRef"
        v-model="paragraphs"
        :folder-menu-items="[]"
        :subscription-menu-items="[]"
        :paragraph-menu-items="paragraphMenuItems"
        :enable-drag="false"
        :enable-selection="true"
        :selected-item-id="selectedParagraphId"
        @dbl-click="handleParagraphClick"
        @drag-end="() => {}"
        @item-select="handleItemSelect"
      />
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">
        <div class="i-material-symbols-format-list-bulleted text-4xl" />
      </div>
      <div class="empty-text">
        <p>暂无文章目录</p>
        <p class="text-sm">请先打开一篇文章</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import "./DirectoryNavigation.css";
import DragableDirectories from "../DragableDirectories/DragableDirectories.vue";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";
import { useRSSStore } from "@/stores/rssStore";
import {
  FolderNode,
  ParagraphNode,
  SubscriptionNode,
} from "@/types/rss/main-data";
import { MenuItem } from "../ContextMenu";

const rssStore = useRSSStore();

// 从 rssStore 获取段落数据
const paragraphs = computed(() => rssStore.currentArticleParagraphs || []);

// 添加选中状态管理
const selectedParagraphId = ref<string | null>(null);

// 监听当前文章段落变化，更新选中状态
watch(
  () => rssStore.currentArticleParagraphs,
  () => {
    selectedParagraphId.value = null;
  },
  { deep: true }
);

// 处理段落选中
const handleItemSelect = (
  item: ParagraphNode | FolderNode | SubscriptionNode
) => {
  if (item.type === "paragraph") {
    selectedParagraphId.value = item.id;

    const element = document.getElementById(item.id);
    const scrollContainer = document.querySelector(
      ".content-wrapper"
    ) as HTMLElement;

    if (element && scrollContainer) {
      // 计算元素相对于滚动容器的位置
      const elementRect = element.getBoundingClientRect();
      const containerRect = scrollContainer.getBoundingClientRect();
      const scrollTop = scrollContainer.scrollTop;
      const elementRelativeTop =
        elementRect.top - containerRect.top + scrollTop;

      // 滚动到元素位置
      scrollContainer.scrollTo({
        top: elementRelativeTop - 20, // 减去20px的偏移量，使元素不完全在顶部
        behavior: "smooth",
      });

      // 添加延迟，确保滚动完成后再更新选中状态
      setTimeout(() => {
        selectedParagraphId.value = item.id;
      }, 300); // 300ms 延迟，与滚动动画时间匹配
    }
  }
};

// 监听滚动事件，自动更新选中状态
const setupScrollListener = () => {
  // 添加防抖函数，避免频繁触发
  let scrollTimeout: number | null = null;
  let scrollContainer: HTMLElement | null = null;

  // 查找滚动容器的函数
  const findScrollContainer = () => {
    return document.querySelector(".content-wrapper") as HTMLElement;
  };

  const handleScroll = () => {
    // 清除之前的定时器
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }

    // 设置新的定时器，延迟100ms执行
    scrollTimeout = window.setTimeout(() => {
      if (!paragraphs.value || paragraphs.value.length === 0) return;

      // 递归获取所有层级的段落元素
      const getAllParagraphElements = (
        paragraphNodes: ParagraphNode[]
      ): HTMLElement[] => {
        const elements: HTMLElement[] = [];

        for (const node of paragraphNodes) {
          if (node.type === "paragraph") {
            const element = document.getElementById(node.id);
            if (element) {
              elements.push(element);
            }

            // 递归处理子段落
            if (node.children && node.children.length > 0) {
              elements.push(...getAllParagraphElements(node.children));
            }
          }
        }

        return elements;
      };

      const paragraphElements = getAllParagraphElements(paragraphs.value);

      if (paragraphElements.length === 0) return;

      // 获取滚动容器的位置信息
      if (!scrollContainer) {
        scrollContainer = findScrollContainer();
      }

      if (!scrollContainer) return;

      const containerRect = scrollContainer.getBoundingClientRect();
      const containerTop = containerRect.top;
      const containerBottom = containerRect.bottom;

      // 设置一个偏移量，用于确定哪个段落被认为是"当前"段落
      const offset = 100; // 距离容器顶部100px内的段落被认为是当前段落

      // 找到在滚动容器中可见的段落
      const visibleParagraphs = paragraphElements.filter((element) => {
        const rect = element.getBoundingClientRect();
        const elementTop = rect.top;
        const elementBottom = rect.bottom;

        // 检查元素是否在滚动容器内可见
        return (
          (elementTop >= containerTop && elementTop <= containerBottom) || // 元素顶部在容器内
          (elementBottom >= containerTop && elementBottom <= containerBottom) || // 元素底部在容器内
          (elementTop <= containerTop && elementBottom >= containerBottom) // 元素完全覆盖容器
        );
      });

      if (visibleParagraphs.length === 0) return;

      // 找到最接近滚动容器顶部偏移位置的段落
      let closestElement = visibleParagraphs[0];
      let minDistance = Math.abs(
        closestElement.getBoundingClientRect().top - (containerTop + offset)
      );

      for (let i = 1; i < visibleParagraphs.length; i++) {
        const element = visibleParagraphs[i];
        const distance = Math.abs(
          element.getBoundingClientRect().top - (containerTop + offset)
        );

        if (distance < minDistance) {
          minDistance = distance;
          closestElement = element;
        }
      }

      // 更新选中状态
      if (closestElement.id !== selectedParagraphId.value) {
        // 查找对应的段落节点以获取层级信息
        const findParagraphNode = (
          nodes: ParagraphNode[],
          targetId: string
        ): ParagraphNode | null => {
          for (const node of nodes) {
            if (node.id === targetId) {
              return node;
            }
            if (node.children && node.children.length > 0) {
              const found = findParagraphNode(node.children, targetId);
              if (found) return found;
            }
          }
          return null;
        };

        const paragraphNode = findParagraphNode(
          paragraphs.value,
          closestElement.id
        );
        console.log(
          "滚动监听：更新选中段落",
          closestElement.id,
          closestElement.textContent?.substring(0, 50),
          "层级:",
          paragraphNode?.level || "未知"
        );
        selectedParagraphId.value = closestElement.id;
      }
    }, 100); // 100ms 防抖延迟
  };

  // 设置轮询检查滚动容器
  const checkScrollContainer = () => {
    const newScrollContainer = findScrollContainer();

    if (newScrollContainer !== scrollContainer) {
      // 如果滚动容器发生变化，移除旧监听，添加新监听
      if (scrollContainer) {
        scrollContainer.removeEventListener("scroll", handleScroll);
      }

      scrollContainer = newScrollContainer;

      if (scrollContainer) {
        console.log("滚动监听：找到滚动容器，开始监听", scrollContainer);
        scrollContainer.addEventListener("scroll", handleScroll);
        // 初始触发一次，确保初始状态正确
        handleScroll();
      } else {
        console.log("滚动监听：未找到滚动容器 .content-wrapper");
      }
    }
  };

  // 初始检查
  checkScrollContainer();

  // 设置轮询，每500ms检查一次滚动容器是否存在
  const pollInterval = setInterval(checkScrollContainer, 500);

  // 组件卸载时清理
  onUnmounted(() => {
    clearInterval(pollInterval);
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }
    if (scrollContainer) {
      scrollContainer.removeEventListener("scroll", handleScroll);
    }
  });
};

// 在组件挂载时设置滚动监听
onMounted(() => {
  setupScrollListener();
});

// 段落菜单项
const paragraphMenuItems: MenuItem[] = [
  {
    label: "滚动到位置",
    action: (paragraph: ParagraphNode) => {
      const element = document.getElementById(paragraph.id);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    },
  },
];

// 处理段落点击
const handleParagraphClick = (item: any) => {
  if (item.type === "paragraph") {
    const paragraph = item as ParagraphNode;

    // 添加调试日志
    console.log("点击段落，ID:", paragraph.id);
    console.log("点击段落:", paragraph);

    const element = document.getElementById(paragraph.id);
    if (element) {
      console.log("找到元素，滚动到位置:", element);
      element.scrollIntoView({ behavior: "smooth" });
    } else {
      console.log("未找到ID为", paragraph.id, "的元素");
    }
  }
};
</script>

<style scoped src="./DirectoryNavigation.css"></style>
