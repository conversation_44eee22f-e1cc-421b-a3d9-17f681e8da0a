// 导入类型定义
import type {
  RSSContentDocument,
  RSSContentItem,
  ArticleListResponse,
} from "@/types/rss/rss-content";
import type {
  SubscriptionNode,
  FolderNode,
  ParagraphNode,
} from "@/types/rss/main-data";
import { saveDocumentWithRetry } from "./dbUtils";
import { getMainData } from "./mainDataService";

/**
 * 创建或更新 RSS 内容文档
 * @param rssDocs RSS 内容文档
 * @returns Promise<RSSContentDocument> 创建或更新后的 RSS 内容文档
 */
export async function createRSSContent(
  rssDocs: RSSContentDocument
): Promise<RSSContentDocument> {
  // 使用通用的文档保存函数，自动处理版本冲突
  return saveDocumentWithRetry(rssDocs);
}

/**
 * 获取指定数据源的文章
 * @param sourceId 可选，数据源ID（订阅源ID或文件夹ID）
 * @param type 可选，数据源类型（"subscription" 或 "folder"）
 * @returns Promise<ArticleListResponse> 文章列表
 * @throws 当只提供 sourceId 或 type 中的一个时，抛出错误
 */
export async function getArticles(
  sourceId?: string,
  type?: "subscription" | "folder"
): Promise<ArticleListResponse> {
  // 检查参数有效性
  if ((sourceId && !type) || (!sourceId && type)) {
    throw new Error("sourceId 和 type 必须同时提供或同时不提供");
  }

  try {
    // 获取主数据
    const mainData = await getMainData();

    // 存储所有需要获取文章的订阅源ID
    const subscriptionIds: string[] = [];

    // 如果没有指定sourceId和type，获取所有订阅源
    if (!sourceId && !type) {
      // 递归查找所有订阅源
      const findAllSubscriptions = (
        nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>
      ) => {
        for (const node of nodes) {
          if (node.type === "subscription") {
            subscriptionIds.push(node.id);
          } else if (node.type === "folder") {
            findAllSubscriptions(node.children);
          }
        }
      };

      findAllSubscriptions(mainData.children);
    }
    // 如果指定了订阅源
    else if (type === "subscription" && sourceId) {
      subscriptionIds.push(sourceId);
    }
    // 如果指定了文件夹
    else if (type === "folder" && sourceId) {
      // 递归查找文件夹下的所有订阅源
      const findFolderAndSubscriptions = (
        nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>
      ): boolean => {
        for (const node of nodes) {
          if (node.type === "folder" && node.id === sourceId) {
            // 找到目标文件夹，收集所有订阅源
            const collectSubscriptions = (
              folderNodes: Array<FolderNode | SubscriptionNode | ParagraphNode>
            ) => {
              for (const folderNode of folderNodes) {
                if (folderNode.type === "subscription") {
                  subscriptionIds.push(folderNode.id);
                } else if (folderNode.type === "folder") {
                  collectSubscriptions(folderNode.children);
                }
              }
            };

            collectSubscriptions(node.children);
            return true;
          } else if (node.type === "folder") {
            if (findFolderAndSubscriptions(node.children)) {
              return true;
            }
          }
        }
        return false;
      };

      findFolderAndSubscriptions(mainData.children);
    }

    // 获取所有订阅源的文章
    const allItems: RSSContentItem[] = [];

    // 如果是文件夹类型，需要按照订阅源在文件夹中的顺序排序
    if (type === "folder" && sourceId) {
      // 获取文件夹下的订阅源顺序
      const folderSubscriptionOrder: string[] = [];

      const findFolderAndCollectOrder = (
        nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>
      ): boolean => {
        for (const node of nodes) {
          if (node.type === "folder" && node.id === sourceId) {
            // 找到目标文件夹，收集订阅源顺序
            const collectSubscriptionOrder = (
              folderNodes: Array<FolderNode | SubscriptionNode | ParagraphNode>
            ) => {
              for (const folderNode of folderNodes) {
                if (folderNode.type === "subscription") {
                  folderSubscriptionOrder.push(folderNode.id);
                } else if (folderNode.type === "folder") {
                  collectSubscriptionOrder(folderNode.children);
                }
              }
            };

            collectSubscriptionOrder(node.children);
            return true;
          } else if (node.type === "folder") {
            if (findFolderAndCollectOrder(node.children)) {
              return true;
            }
          }
        }
        return false;
      };

      findFolderAndCollectOrder(mainData.children);

      // 按照订阅源顺序获取文章
      for (const subscriptionId of folderSubscriptionOrder) {
        try {
          const docId = `rss-browser/content/${subscriptionId}`;
          const rssContent = (await utools.db.get(
            docId
          )) as RSSContentDocument | null;

          if (rssContent && rssContent.items && rssContent.items.length > 0) {
            // 按照文章发布时间排序（最新的在前）
            const sortedItems = [...rssContent.items].sort((a, b) => {
              const dateA = new Date(a.pubDate).getTime();
              const dateB = new Date(b.pubDate).getTime();
              return dateB - dateA; // 降序排列，最新的在前
            });

            allItems.push(...sortedItems);
          }
        } catch (error) {
          console.error(`获取订阅源 ${subscriptionId} 的文章失败:`, error);
          // 继续处理其他订阅源
        }
      }
    } else {
      // 如果是所有订阅源或单个订阅源，直接按照时间排序
      const subscriptionItems: Array<{
        subscriptionId: string;
        items: RSSContentItem[];
      }> = [];

      // 按照订阅源排序（按照主数据中的顺序）
      for (const subscriptionId of subscriptionIds) {
        try {
          const docId = `rss-browser/content/${subscriptionId}`;
          const rssContent = (await utools.db.get(
            docId
          )) as RSSContentDocument | null;

          if (rssContent && rssContent.items && rssContent.items.length > 0) {
            // 按照文章发布时间排序（最新的在前）
            const sortedItems = [...rssContent.items].sort((a, b) => {
              const dateA = new Date(a.pubDate).getTime();
              const dateB = new Date(b.pubDate).getTime();
              return dateB - dateA; // 降序排列，最新的在前
            });

            subscriptionItems.push({
              subscriptionId,
              items: sortedItems,
            });
          }
        } catch (error) {
          console.error(`获取订阅源 ${subscriptionId} 的文章失败:`, error);
          // 继续处理其他订阅源
        }
      }

      // 如果是所有订阅源，需要按照文章发布时间排序
      if (!sourceId && !type) {
        // 将所有文章合并到一个数组
        for (const { items } of subscriptionItems) {
          allItems.push(...items);
        }

        // 按照文章发布时间排序（最新的在前）
        allItems.sort((a, b) => {
          const dateA = new Date(a.pubDate).getTime();
          const dateB = new Date(b.pubDate).getTime();
          return dateB - dateA; // 降序排列，最新的在前
        });
      } else {
        // 如果是单个订阅源，直接添加文章
        for (const { items } of subscriptionItems) {
          allItems.push(...items);
        }
      }
    }

    return {
      items: allItems,
      totalCount: allItems.length,
    };
  } catch (error) {
    console.error("获取文章失败:", error);
    throw error;
  }
}

/**
 * 删除RSS内容文档
 * @param sourceId RSS源ID
 * @returns Promise<boolean> 是否成功删除
 */
export async function deleteRSSContent(sourceId: string): Promise<boolean> {
  try {
    const docId = `rss-browser/content/${sourceId}`;

    // 尝试获取现有文档
    const doc = await utools.db.get(docId);

    if (doc != null) {
      // 如果存在，删除它
      const result = await utools.db.remove(doc);

      if (result.ok) {
        console.log(`RSS内容删除成功: ${sourceId}`);
        return true;
      } else {
        throw new Error(result.message || "删除RSS内容失败");
      }
    }

    return false;
  } catch (error: any) {
    // 如果是 404 错误（文档不存在），视为删除成功
    if (error.status === 404) {
      console.log(`RSS内容不存在，无需删除: ${sourceId}`);
      return true;
    }

    console.error(`删除RSS内容失败: ${sourceId}`, error);
    throw error;
  }
}

/**
 * 递归查找文件夹下的所有订阅源ID
 * @param nodes 节点数组
 * @param folderId 文件夹ID
 * @returns string[] 订阅源ID数组
 */
function findSubscriptionIdsInFolder(
  nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>,
  folderId: string
): string[] {
  const subscriptionIds: string[] = [];

  for (const node of nodes) {
    if (node.type === "folder" && node.id === folderId) {
      // 找到目标文件夹，收集所有订阅源
      const collectSubscriptions = (
        folderNodes: Array<FolderNode | SubscriptionNode | ParagraphNode>
      ) => {
        for (const folderNode of folderNodes) {
          if (folderNode.type === "subscription") {
            subscriptionIds.push(folderNode.id);
          } else if (folderNode.type === "folder") {
            collectSubscriptions(folderNode.children);
          }
        }
      };

      collectSubscriptions(node.children);
      return subscriptionIds;
    } else if (node.type === "folder") {
      // 递归查找子文件夹
      const foundIds = findSubscriptionIdsInFolder(node.children, folderId);
      if (foundIds.length > 0) {
        return foundIds;
      }
    }
  }

  return subscriptionIds;
}

/**
 * 删除文件夹下的所有RSS内容
 * @param folderId 文件夹ID
 * @returns Promise<boolean> 是否成功删除
 */
export async function deleteFolderRSSContents(
  folderId: string
): Promise<boolean> {
  try {
    // 获取主数据
    const mainData = await getMainData();

    // 查找文件夹下的所有订阅源ID
    const subscriptionIds = findSubscriptionIdsInFolder(
      mainData.children,
      folderId
    );

    if (subscriptionIds.length === 0) {
      console.log(`文件夹 ${folderId} 下没有订阅源`);
      return true;
    }

    // 删除所有订阅源的RSS内容
    let allDeleted = true;
    for (const subscriptionId of subscriptionIds) {
      try {
        const deleted = await deleteRSSContent(subscriptionId);
        if (!deleted) {
          allDeleted = false;
        }
      } catch (error) {
        console.error(`删除订阅源 ${subscriptionId} 的RSS内容失败:`, error);
        allDeleted = false;
      }
    }

    return allDeleted;
  } catch (error) {
    console.error(`删除文件夹 ${folderId} 的RSS内容失败:`, error);
    throw error;
  }
}
