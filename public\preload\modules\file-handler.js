"use strict";
/**
 * 文件操作模块示例
 * 这个文件展示了如何创建独立的功能模块
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileHandler = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class FileHandler {
    /**
     * 处理文件读取
     */
    handleRead(payload) {
        if (!payload || payload.length === 0) {
            utools.showNotification("请选择要读取的文件");
            return;
        }
        const file = payload[0];
        const filePath = file.path;
        try {
            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
                throw new Error("文件不存在");
            }
            // 获取文件信息
            const stats = fs.statSync(filePath);
            const fileInfo = {
                name: file.name,
                path: filePath,
                size: stats.size,
                modified: stats.mtime,
                extension: path.extname(filePath).toLowerCase(),
            };
            // 读取文件内容
            let content;
            const textExtensions = [
                ".txt",
                ".md",
                ".json",
                ".js",
                ".ts",
                ".css",
                ".html",
            ];
            if (textExtensions.includes(fileInfo.extension)) {
                content = fs.readFileSync(filePath, "utf8");
            }
            else {
                content = "不支持的文件类型";
            }
            // 将数据传递给渲染进程
            window.utoolsPayload = {
                type: "file-read",
                data: { fileInfo, content },
            };
            // 跳转到读取页面
            utools.redirect("read", { type: "text", data: { fileInfo, content } });
        }
        catch (error) {
            console.error("文件读取错误:", error);
            utools.showNotification("文件读取失败: " + error.message);
        }
    }
    /**
     * 保存文件
     */
    async saveFile(content, filename) {
        try {
            const savePath = utools.showSaveDialog({
                title: "保存文件",
                defaultPath: filename,
                filters: [
                    { name: "All Files", extensions: ["*"] },
                    { name: "Text Files", extensions: ["txt"] },
                    { name: "JSON Files", extensions: ["json"] },
                ],
            });
            if (savePath) {
                fs.writeFileSync(savePath, content, "utf8");
                utools.showNotification("文件保存成功");
                return true;
            }
            return false;
        }
        catch (error) {
            console.error("保存文件错误:", error);
            utools.showNotification("文件保存失败");
            return false;
        }
    }
    /**
     * 在文件夹中显示文件
     */
    showInFolder(filePath) {
        utools.shellShowItemInFolder(filePath);
    }
    /**
     * 用默认程序打开文件
     */
    openFile(filePath) {
        utools.shellOpenPath(filePath);
    }
}
exports.FileHandler = FileHandler;
