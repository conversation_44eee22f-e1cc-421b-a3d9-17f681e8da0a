# DragableDirectories 展开/折叠动画功能

## ✨ 新增功能

我已经为 `DragableDirectories.vue` 组件添加了完整的点击展开/折叠功能和流畅的动画效果。

### 🎯 **核心功能**

1. **点击展开/折叠**：点击文件夹可以展开或折叠子目录
2. **视觉反馈**：不同状态的文件夹显示不同图标
3. **流畅动画**：展开/折叠时有平滑的动画过渡
4. **交互体验**：悬停效果和视觉提示

## 🔧 **实现细节**

### 1. **模板结构更新**

```vue
<template>
  <div class="drag-area" ref="el">
    <div
      v-for="(el, index) in modelValue"
      :key="el.name"
      class="directory-item"
    >
      <!-- 可点击的目录头部 -->
      <div
        class="directory-header flex justify-start items-center cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-2 py-1 transition-colors duration-200"
        @click="toggleFolder(el)"
      >
        <!-- 展开/折叠箭头 -->
        <div
          v-if="isFolder(el)"
          class="expand-arrow mr-2 transition-transform duration-300 ease-in-out"
          :class="{ 'rotate-90': el.isExpanded }"
        >
          <div class="i-material-symbols-chevron-right text-gray-500"></div>
        </div>

        <!-- 动态文件夹图标 -->
        <div
          :class="`${
            isFolder(el)
              ? el.isExpanded
                ? 'i-material-symbols-folder-open-outline'
                : 'i-material-symbols-folder-outline'
              : 'i-material-symbols-files-outline'
          } mr-2 ${iconColor(index)} transition-all duration-200`"
        />

        <!-- 文件/文件夹名称 -->
        <div class="select-none">{{ el.name }}</div>
      </div>

      <!-- 子目录容器（带动画） -->
      <div
        v-if="isFolder(el)"
        class="children-container ml-2 overflow-hidden transition-all duration-300 ease-in-out"
        :class="{
          'max-h-0 opacity-0': !el.isExpanded,
          'max-h-screen opacity-100': el.isExpanded,
        }"
      >
        <nested-function v-model="el.children" />
      </div>
    </div>
  </div>
</template>
```

### 2. **JavaScript 逻辑**

```typescript
// 判断是否是文件夹
const isFolder = (item: IList) => {
  return item.children && item.children.length >= 0;
};

// 切换文件夹展开/折叠状态
const toggleFolder = (item: IList) => {
  if (isFolder(item)) {
    item.isExpanded = !item.isExpanded;
  }
};
```

### 3. **接口定义**

```typescript
interface IList {
  name: string;
  children?: IList[];
  isExpanded?: boolean; // 新增：展开状态
}
```

## 🎨 **动画效果**

### 1. **箭头旋转动画**

- 使用 `transition-transform duration-300 ease-in-out`
- 展开时箭头旋转 90 度：`rotate-90`
- 平滑的缓动函数：`ease-in-out`

### 2. **文件夹图标切换**

- 折叠状态：`i-material-symbols-folder-outline`
- 展开状态：`i-material-symbols-folder-open-outline`
- 平滑过渡：`transition-all duration-200`

### 3. **子目录展开/折叠动画**

- 高度动画：`max-h-0` ↔ `max-h-screen`
- 透明度动画：`opacity-0` ↔ `opacity-100`
- 时长：`duration-300`
- 缓动：`ease-in-out`

### 4. **悬停效果**

- 背景色变化：`hover:bg-gray-100 dark:hover:bg-gray-800`
- 轻微位移：`transform: translateX(2px)`
- 箭头背景高亮：`background-color: rgba(0, 0, 0, 0.1)`

## 🎯 **视觉特点**

### 1. **状态指示**

- **📁 折叠文件夹**：显示关闭的文件夹图标
- **📂 展开文件夹**：显示打开的文件夹图标
- **📄 文件**：显示文件图标，无箭头
- **▶ 箭头**：指向右侧表示折叠，指向下方表示展开

### 2. **交互反馈**

- **鼠标悬停**：背景色变化 + 轻微位移
- **点击反馈**：即时的状态切换
- **动画流畅**：300ms 的平滑过渡
- **视觉层次**：子目录有适当的缩进（`ml-6`）

### 3. **响应式设计**

- 支持深色模式：`dark:hover:bg-gray-800`
- 适配不同屏幕尺寸
- 触摸友好的点击区域

## 📋 **测试数据**

在 `Navigation.vue` 中提供了完整的测试数据：

```typescript
const list = ref([
  {
    name: "文件夹1",
    children: [
      { name: "文件1" },
      { name: "文件2" },
      {
        name: "子文件夹1",
        children: [{ name: "子文件1" }, { name: "子文件2" }],
        isExpanded: false,
      },
    ],
    isExpanded: true, // 默认展开
  },
  {
    name: "文件夹2",
    children: [{ name: "文件3" }, { name: "文件4" }],
    isExpanded: false, // 默认折叠
  },
  {
    name: "空文件夹",
    children: [],
    isExpanded: false,
  },
]);
```

## 🚀 **使用方法**

### 1. **启动项目**

```bash
pnpm dev
# 访问 http://localhost:5174/
```

### 2. **测试功能**

- 点击文件夹名称或图标区域
- 观察箭头旋转动画
- 观察文件夹图标切换
- 观察子目录的展开/折叠动画
- 测试多级嵌套的展开/折叠

### 3. **拖拽功能**

- 展开/折叠功能与拖拽功能完全兼容
- 可以在展开状态下拖拽重排序
- 拖拽不会影响展开/折叠状态

## 🎨 **样式特点**

### 1. **现代化设计**

- 圆角边框：`rounded`
- 柔和阴影和背景
- 统一的间距和对齐

### 2. **流畅动画**

- 使用 CSS3 `transition` 属性
- 贝塞尔曲线缓动：`cubic-bezier(0.4, 0, 0.2, 1)`
- 关键帧动画：`@keyframes slideDown/slideUp`

### 3. **无障碍设计**

- 明确的视觉状态指示
- 合适的点击区域大小
- 键盘导航友好（可扩展）

## ✨ **总结**

这次更新为 `DragableDirectories.vue` 组件添加了：

- ✅ **完整的展开/折叠功能**
- ✅ **流畅的动画效果**
- ✅ **直观的视觉反馈**
- ✅ **现代化的交互体验**
- ✅ **与拖拽功能的完美兼容**

组件现在具有了完整的文件树功能，用户体验得到显著提升！🎉
