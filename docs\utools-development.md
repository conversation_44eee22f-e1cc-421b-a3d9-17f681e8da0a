# uTools 插件开发指南

## 🏗️ 架构概述

uTools 插件基于 Electron 架构，包含三个主要部分：

### 1. 主进程 (Main Process)

- uTools 应用本身
- 负责系统级操作和窗口管理
- 通过 uTools API 提供功能

### 2. 预加载脚本 (Preload Script)

- 运行在独立的上下文中
- 桥接主进程和渲染进程
- 可以访问 Node.js API 和 uTools API
- **文件位置**: `public/preload/services.ts`

### 3. 渲染进程 (Renderer Process)

- 您的 Vue 应用界面
- 运行在浏览器环境中
- 通过 `window.utools` 与预加载脚本通信

## 🔧 预加载脚本开发

### 基本结构

```typescript
// public/preload/services.ts

// 插件进入事件
utools.onPluginEnter(({ code, type, payload }) => {
  console.log("用户进入插件", code, type, payload);

  // 根据不同的 code 执行不同逻辑
  switch (code) {
    case "home":
      // 主页逻辑
      break;
    case "read":
      // 文件读取逻辑
      handleFileRead(payload);
      break;
    case "write":
      // 文件写入逻辑
      handleFileWrite(payload);
      break;
  }
});

// 插件退出事件
utools.onPluginOut(() => {
  console.log("用户退出插件");
  // 清理资源
});

// 文件读取处理
function handleFileRead(payload: any) {
  if (payload && payload.length > 0) {
    const filePath = payload[0].path;
    // 使用 Node.js fs 模块读取文件
    const fs = require("fs");
    const content = fs.readFileSync(filePath, "utf8");

    // 将数据传递给渲染进程
    utools.redirect("read", { filePath, content });
  }
}
```

### 常用 uTools API

```typescript
// 1. 窗口操作
utools.hideMainWindow(); // 隐藏主窗口
utools.showMainWindow(); // 显示主窗口
utools.setExpendHeight(600); // 设置窗口高度

// 2. 系统操作
utools.shellOpenExternal(url); // 打开外部链接
utools.shellShowItemInFolder(path); // 在文件夹中显示文件

// 3. 剪贴板操作
const text = utools.copyText(); // 获取剪贴板文本
utools.copyText("新内容"); // 设置剪贴板文本
const image = utools.copyImage(); // 获取剪贴板图片

// 4. 数据库操作
utools.db.put({
  _id: "key",
  data: "value",
});
const doc = utools.db.get("key");

// 5. 路由跳转
utools.redirect("page-code", payload);
```

## 🚀 构建流程优化

### 当前问题

- 需要在 `public/preload` 目录单独构建
- 生成的 `dist` 目录需要手动处理
- 构建流程繁琐

### 优化方案

#### 1. 统一构建命令

```bash
# 开发模式（自动构建预加载脚本）
pnpm run dev

# 生产构建（包含预加载脚本）
pnpm run build

# 单独构建预加载脚本
pnpm run build:preload
```

#### 2. 自动化脚本

- `scripts/build-preload.js` - 自动编译和复制预加载脚本
- TypeScript 输出直接到 `public/preload/` 目录
- 避免临时 `dist` 目录

#### 3. 开发模式监听

```bash
# 监听预加载脚本变化
pnpm run dev:preload
```

## 📁 项目结构

```
├── public/
│   ├── preload/
│   │   ├── services.ts      # 预加载脚本源码
│   │   ├── services.js      # 编译后的脚本
│   │   ├── package.json     # 预加载脚本配置
│   │   └── tsconfig.json    # TypeScript 配置
│   ├── plugin.json          # 插件配置
│   └── logo.png            # 插件图标
├── src/                    # Vue 应用源码
├── dist/                   # 构建输出
└── scripts/
    └── build-preload.js    # 预加载脚本构建工具
```

## 🔍 调试技巧

### 1. 预加载脚本调试

```typescript
// 在预加载脚本中使用 console.log
console.log("预加载脚本日志", data);

// uTools 开发者工具中查看日志
// 右键 uTools -> 检查元素 -> Console
```

### 2. 渲染进程调试

```typescript
// 在 Vue 组件中
console.log("渲染进程日志", window.utools);

// 检查 uTools API 是否可用
if (window.utools) {
  console.log("uTools API 可用");
} else {
  console.log("运行在浏览器环境");
}
```

### 3. 数据传递调试

```typescript
// 预加载脚本 -> 渲染进程
utools.onPluginEnter(({ code, payload }) => {
  console.log('接收到数据:', payload);
  // 传递给 Vue 应用
  window.electronAPI = { code, payload };
});

// Vue 组件中接收
mounted() {
  if (window.electronAPI) {
    console.log('从预加载脚本接收:', window.electronAPI);
  }
}
```

## 📋 最佳实践

### 1. 错误处理

```typescript
utools.onPluginEnter(({ code, payload }) => {
  try {
    handlePluginEnter(code, payload);
  } catch (error) {
    console.error("插件进入错误:", error);
    utools.showNotification("操作失败: " + error.message);
  }
});
```

### 2. 异步操作

```typescript
// 使用 async/await
utools.onPluginEnter(async ({ code, payload }) => {
  if (code === "read") {
    try {
      const content = await readFileAsync(payload[0].path);
      utools.redirect("read", { content });
    } catch (error) {
      utools.showNotification("文件读取失败");
    }
  }
});
```

### 3. 类型安全

```typescript
// 定义类型
interface FilePayload {
  path: string;
  name: string;
  size: number;
}

interface PluginEnterData {
  code: string;
  type: string;
  payload: FilePayload[];
}

utools.onPluginEnter((data: PluginEnterData) => {
  // TypeScript 类型检查
});
```

这个指南涵盖了 uTools 插件开发的核心概念和最佳实践，帮助您更高效地开发插件功能。
