<template>
  <nav class="bookmarks-navigation">
    <div class="nav-header">
      <input
        type="text"
        class="tag-input"
        placeholder="搜索文章标题、作者、标签或内容..."
        v-model="searchQuery"
        @input="handleSearchInput"
      />
    </div>

    <div class="bookmarks-list">
      <!-- 书签列表 -->
      <div
        v-for="article in filteredArticles"
        :key="article.id"
        class="bookmark-item"
        @click="navigateToArticle(article)"
      >
        <div class="bookmark-cover">
          <img
            v-if="getArticleImage(article)"
            :src="getArticleImage(article)"
            :alt="article.title"
            class="cover-image"
            @error="handleImageError"
          />
          <div
            v-else
            class="cover-placeholder"
            :style="{ backgroundColor: getAvatarColor(article.title) }"
          >
            {{ getTitleInitial(article.title) }}
          </div>
        </div>
        <div class="bookmark-content">
          <div class="bookmark-title" :title="article.title">
            {{ article.title }}
          </div>
          <div class="bookmark-header">
            <span class="bookmark-author" :title="article.creator">
              {{ article.creator || "未知作者" }}
            </span>
            <span class="bookmark-time">
              {{ formatTimeAgo(article.pubDate) }}
            </span>
          </div>
          <!-- <div v-if="getCustomTags(article).length > 0" class="bookmark-tags">
            <span
              v-for="tag in getCustomTags(article)"
              :key="tag"
              class="bookmark-tag"
            >
              {{ tag }}
            </span>
          </div> -->
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRSSStore } from "@/stores/rssStore";
import type { RSSContentItem } from "@/types/rss/rss-content";
import {
  getAvatarColor,
  getTitleInitial,
  getArticleImage,
  handleImageError,
  formatTimeAgo,
} from "@/services/utils";
import { useRouter } from "vue-router";

const rssStore = useRSSStore();
const router = useRouter();

const navigateToArticle = (article: RSSContentItem) => {
  // 将文章数据存储到全局变量
  (window as any).currentArticle = article;

  // 检查当前是否已经在文章页面
  if (router.currentRoute.value.name === "RSSArticle") {
    // 如果已经在文章页面，触发一个自定义事件来通知文章组件更新
    window.dispatchEvent(
      new CustomEvent("article-changed", { detail: article })
    );
  } else {
    // 如果不在文章页面，导航到文章详情页面
    router.push({ name: "RSSArticle" });
  }
};

// 搜索相关
const searchQuery = ref<string>("");
const searchTags = ref<string[]>([]);

// 处理搜索输入
const handleSearchInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  searchQuery.value = target.value;

  // 将搜索查询转换为标签数组进行过滤
  if (searchQuery.value.trim()) {
    searchTags.value = [searchQuery.value.trim()];
  } else {
    searchTags.value = [];
  }
};

// 获取所有文章
const allArticles = computed(() => {
  return rssStore.articles;
});

// 根据搜索标签过滤文章
const filteredArticles = computed(() => {
  if (searchTags.value.length === 0) {
    return allArticles.value;
  }

  return allArticles.value.filter((article) => {
    // 检查文章是否包含搜索的标签
    return searchTags.value.some((searchTag) => {
      // 搜索标题
      if (article.title.toLowerCase().includes(searchTag.toLowerCase())) {
        return true;
      }
      // 搜索作者
      if (article.creator?.toLowerCase().includes(searchTag.toLowerCase())) {
        return true;
      }
      // 搜索自定义标签
      if (
        article.marks?.some(
          (mark) =>
            mark.startsWith("#") &&
            mark.toLowerCase().includes(searchTag.toLowerCase())
        )
      ) {
        return true;
      }
      // 搜索内容摘要
      if (
        article.contentSnippet?.toLowerCase().includes(searchTag.toLowerCase())
      ) {
        return true;
      }
      return false;
    });
  });
});

// 组件挂载时获取数据
onMounted(() => {
  if (allArticles.value.length === 0) {
    rssStore.fetchAllData();
  }
});
</script>

<style scoped src="./BookmarksNavigation.css"></style>
