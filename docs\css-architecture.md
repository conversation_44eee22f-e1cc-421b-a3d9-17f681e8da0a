# CSS 架构设计文档

## 🎯 设计目标

为 utools 插件项目建立一个可维护、可扩展的 CSS 架构，结合 UnoCSS 原子化 CSS 的优势和传统组件化开发的便利性。

## 📁 架构方案：混合式组织

我们采用了**混合式组织方案**，根据组件复杂度和维护需求选择不同的样式组织方式：

### 1. 复杂组件 - 就近原则 ✅

**适用场景**：
- 组件逻辑复杂，样式较多
- 需要频繁修改和维护
- 团队协作中需要独立开发

**组织方式**：
```
src/components/ComponentName/
├── ComponentName.vue    # 组件文件
├── ComponentName.css    # 组件样式
└── index.ts            # 导出文件
```

**示例**：Layout、Navigation 组件

**优势**：
- 样式与组件逻辑就近，便于维护
- 组件自包含，降低耦合度
- 便于团队协作，减少冲突
- 支持组件级别的样式优化

### 2. 简单组件 - 集中管理 ✅

**适用场景**：
- 组件逻辑简单，样式较少
- 样式相对稳定，修改频率低
- 需要全局样式复用

**组织方式**：
```
src/styles/components/
├── hello.css           # Hello 组件样式
└── pinia.css          # Pinia 页面样式
```

**优势**：
- 便于全局样式管理和复用
- 减少文件数量，简化项目结构
- 便于样式合并和优化

### 3. 页面样式 - 统一管理 ✅

**组织方式**：
```
src/styles/pages/
├── home.css           # Home 页面样式
└── read-write.css     # Read/Write 页面样式
```

### 4. 全局样式 - 集中管理 ✅

**组织方式**：
```
src/styles/
├── index.css          # 主样式入口
└── global.css         # 全局样式和重置
```

## 🔧 技术实现

### UnoCSS 集成

1. **原子类优先**：使用 UnoCSS 原子类处理大部分样式需求
2. **@apply 指令**：在 CSS 文件中使用 @apply 组合原子类
3. **自定义快捷方式**：定义常用的组件样式快捷方式

### 样式导入策略

1. **组件内导入**：复杂组件在 Vue 文件中直接导入样式
   ```vue
   <script setup>
   import './ComponentName.css'
   </script>
   ```

2. **全局导入**：简单组件样式通过主样式文件导入
   ```css
   /* src/styles/index.css */
   @import './components/hello.css';
   @import './pages/home.css';
   ```

## 📋 最佳实践

### 1. 组件样式编写

```css
/* 使用 @apply 指令组合原子类 */
.component-name {
  @apply flex items-center p-4 bg-white rounded-lg shadow-md;
}

/* 复杂样式使用传统 CSS */
.component-name::before {
  content: '';
  position: absolute;
  /* ... */
}
```

### 2. 响应式设计

```css
/* 移动端优先 */
.component-name {
  @apply w-full;
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .component-name {
    @apply w-64;
  }
}
```

### 3. 深色模式支持

```css
/* 浅色模式 */
.component-name {
  @apply bg-white text-gray-900;
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .component-name {
    @apply bg-gray-800 text-white;
  }
}
```

## 🚀 迁移指南

### 从传统 CSS 迁移到混合架构

1. **评估组件复杂度**
   - 复杂组件 → 创建组件目录，样式就近放置
   - 简单组件 → 保持集中管理

2. **重构样式代码**
   - 使用 UnoCSS 原子类替换常见样式
   - 保留复杂样式的传统 CSS 写法
   - 使用 @apply 指令组合原子类

3. **更新导入方式**
   - 复杂组件在 Vue 文件中导入样式
   - 简单组件通过主样式文件导入

## 📊 架构优势

1. **可维护性**：样式与组件逻辑就近，便于维护
2. **可扩展性**：支持不同复杂度组件的灵活组织
3. **性能优化**：UnoCSS 按需生成，减少 CSS 体积
4. **开发体验**：结合原子类和传统 CSS 的优势
5. **团队协作**：清晰的组织规则，减少冲突

## 🔍 监控和优化

1. **使用 UnoCSS Inspector** 监控生成的 CSS
2. **定期审查样式文件大小**和重复代码
3. **优化自定义快捷方式**，提高复用性
4. **保持样式组织的一致性**

这种混合式架构既保持了现代化开发的便利性，又确保了项目的长期可维护性。
