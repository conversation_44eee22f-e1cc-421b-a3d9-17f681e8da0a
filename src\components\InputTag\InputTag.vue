<template>
  <div class="input-tag-container" :class="{ compact: props.compact }">
    <!-- 主输入框 -->
    <div
      class="input-tag-wrapper"
      :class="{ 'is-focused': isFocused, 'has-dropdown': showDropdown }"
    >
      <!-- 已选择的标签 -->
      <div class="selected-tags">
        <span
          v-for="(tag, index) in selectedTags"
          :key="index"
          class="tag-item"
        >
          {{ tag }}
          <button @click="removeTag(index)" class="tag-remove" type="button">
            <div class="i-material-symbols-close text-lg" />
          </button>
        </span>
      </div>

      <!-- 输入框 -->
      <input
        ref="inputRef"
        v-model="inputValue"
        type="text"
        :placeholder="placeholder"
        class="tag-input"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
        @input="handleInput"
      />

      <!-- 清空按钮 -->
      <button
        v-if="selectedTags.length > 0 || inputValue"
        @click="clearAll"
        class="clear-button"
        type="button"
      >
        <div class="i-material-symbols-close text-sm" />
      </button>
    </div>

    <!-- 下拉选项列表 -->
    <div
      v-if="showDropdown && filteredOptions.length > 0"
      class="dropdown-menu"
    >
      <div
        v-for="(option, index) in filteredOptions"
        :key="option"
        :class="[
          'dropdown-item',
          { 'is-highlighted': highlightedIndex === index },
          { 'is-selected': selectedTags.includes(option) },
        ]"
        @click="selectOption(option)"
        @mouseenter="highlightedIndex = index"
      >
        <span class="option-text">{{ option }}</span>
        <div
          v-if="selectedTags.includes(option)"
          class="i-material-symbols-check text-sm text-blue-400"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from "vue";

interface Props {
  modelValue?: string[];
  placeholder?: string;
  options?: string[];
  maxTags?: number;
  allowCustom?: boolean;
  compact?: boolean;
}

interface Emits {
  (e: "update:modelValue", value: string[]): void;
  (e: "change", value: string[]): void;
  (e: "add", tag: string): void;
  (e: "remove", tag: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  placeholder: "输入标签并按 Enter 添加...",
  options: () => [],
  maxTags: 10,
  allowCustom: true,
  compact: false,
});

const emit = defineEmits<Emits>();

// 响应式数据
const inputRef = ref<HTMLInputElement>();
const inputValue = ref("");
const isFocused = ref(false);
const showDropdown = ref(false);
const highlightedIndex = ref(-1);

// 计算属性
const selectedTags = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit("update:modelValue", value);
    emit("change", value);
  },
});

const filteredOptions = computed(() => {
  if (!inputValue.value) return props.options.slice(0, 8);

  return props.options
    .filter(
      (option) =>
        option.toLowerCase().includes(inputValue.value.toLowerCase()) &&
        !selectedTags.value.includes(option)
    )
    .slice(0, 8);
});

// 方法
const handleFocus = () => {
  isFocused.value = true;
  showDropdown.value = true;
  highlightedIndex.value = -1;
};

const handleBlur = () => {
  // 延迟隐藏下拉菜单，以便点击选项
  setTimeout(() => {
    isFocused.value = false;
    showDropdown.value = false;
    highlightedIndex.value = -1;
  }, 150);
};

const handleInput = () => {
  showDropdown.value = true;
  highlightedIndex.value = -1;
};

const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case "Enter":
      event.preventDefault();
      if (
        highlightedIndex.value >= 0 &&
        filteredOptions.value[highlightedIndex.value]
      ) {
        selectOption(filteredOptions.value[highlightedIndex.value]);
      } else if (inputValue.value.trim() && props.allowCustom) {
        addTag(inputValue.value.trim());
      }
      break;

    case "ArrowDown":
      event.preventDefault();
      if (showDropdown.value && filteredOptions.value.length > 0) {
        highlightedIndex.value = Math.min(
          highlightedIndex.value + 1,
          filteredOptions.value.length - 1
        );
      }
      break;

    case "ArrowUp":
      event.preventDefault();
      if (showDropdown.value && filteredOptions.value.length > 0) {
        highlightedIndex.value = Math.max(highlightedIndex.value - 1, 0);
      }
      break;

    case "Escape":
      showDropdown.value = false;
      highlightedIndex.value = -1;
      inputRef.value?.blur();
      break;

    case "Backspace":
      if (!inputValue.value && selectedTags.value.length > 0) {
        removeTag(selectedTags.value.length - 1);
      }
      break;
  }
};

const addTag = (tag: string) => {
  if (
    !tag ||
    selectedTags.value.includes(tag) ||
    selectedTags.value.length >= props.maxTags
  ) {
    return;
  }

  const newTags = [...selectedTags.value, tag];
  selectedTags.value = newTags;
  emit("add", tag);
  inputValue.value = "";
  showDropdown.value = false;
};

const removeTag = (index: number) => {
  const removedTag = selectedTags.value[index];
  const newTags = selectedTags.value.filter((_, i) => i !== index);
  selectedTags.value = newTags;
  emit("remove", removedTag);
};

const selectOption = (option: string) => {
  addTag(option);
  nextTick(() => {
    inputRef.value?.focus();
  });
};

const clearAll = () => {
  selectedTags.value = [];
  inputValue.value = "";
  showDropdown.value = false;
  nextTick(() => {
    inputRef.value?.focus();
  });
};

// 点击外部关闭下拉菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  if (!target.closest(".input-tag-container")) {
    showDropdown.value = false;
  }
};

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>

<style scoped src="./InputTag.css"></style>
