/**
 * @deprecated 此文件已被拆分为多个模块化文件
 * 请使用 @/services/utils/ 目录下的具体模块
 *
 * 迁移指南：
 * - Vue响应式处理: import { cleanReactiveObject, deepCopyWithoutReactive } from "@/services/utils/reactiveUtils"
 * - 图片处理: import { extractFirstImageFromContent, handleImageError } from "@/services/utils/imageUtils"
 * - 日期处理: import { formatDate, formatTimeAgo } from "@/services/utils/dateUtils"
 * - UI工具: import { getAvatarColor, getTitleInitial } from "@/services/utils/uiUtils"
 * - 文章工具: import { getCustomTags, getArticleImage } from "@/services/utils/articleUtils"
 * - 或统一导入: import { ... } from "@/services/utils"
 */

// 为了向后兼容，重新导出所有函数
export {
  // Vue 响应式对象处理
  cleanReactiveObject,
  deepCopyWithoutReactive,
} from "./utils/reactiveUtils";

export {
  // 图片处理
  extractFirstImageFromContent,
  handleImageError,
} from "./utils/imageUtils";

export {
  // 日期时间处理
  formatDate,
  formatTimeAgo,
} from "./utils/dateUtils";

export {
  // UI 相关
  getAvatarColor,
  getTitleInitial,
} from "./utils/uiUtils";

export {
  // 文章相关
  getCustomTags,
  getArticleImage,
} from "./utils/articleUtils";
