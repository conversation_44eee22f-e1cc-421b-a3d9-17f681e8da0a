/**
 * 文件操作模块示例
 * 这个文件展示了如何创建独立的功能模块
 */

import * as fs from "fs";
import * as path from "path";
import type { FileInfo } from "../types/global";

export class FileHandler {
  /**
   * 处理文件读取
   */
  handleRead(payload: any) {
    if (!payload || payload.length === 0) {
      utools.showNotification("请选择要读取的文件");
      return;
    }

    const file = payload[0];
    const filePath = file.path;

    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        throw new Error("文件不存在");
      }

      // 获取文件信息
      const stats = fs.statSync(filePath);
      const fileInfo: FileInfo = {
        name: file.name,
        path: filePath,
        size: stats.size,
        modified: stats.mtime,
        extension: path.extname(filePath).toLowerCase(),
      };

      // 读取文件内容
      let content: string;
      const textExtensions = [
        ".txt",
        ".md",
        ".json",
        ".js",
        ".ts",
        ".css",
        ".html",
      ];

      if (textExtensions.includes(fileInfo.extension)) {
        content = fs.readFileSync(filePath, "utf8");
      } else {
        content = "不支持的文件类型";
      }

      // 将数据传递给渲染进程
      (window as any).utoolsPayload = {
        type: "file-read",
        data: { fileInfo, content },
      };

      // 跳转到读取页面
      utools.redirect("read", { type: "text", data: { fileInfo, content } });
    } catch (error) {
      console.error("文件读取错误:", error);
      utools.showNotification("文件读取失败: " + (error as Error).message);
    }
  }

  /**
   * 保存文件
   */
  async saveFile(content: string, filename: string): Promise<boolean> {
    try {
      const savePath = utools.showSaveDialog({
        title: "保存文件",
        defaultPath: filename,
        filters: [
          { name: "All Files", extensions: ["*"] },
          { name: "Text Files", extensions: ["txt"] },
          { name: "JSON Files", extensions: ["json"] },
        ],
      });

      if (savePath) {
        fs.writeFileSync(savePath, content, "utf8");
        utools.showNotification("文件保存成功");
        return true;
      }
      return false;
    } catch (error) {
      console.error("保存文件错误:", error);
      utools.showNotification("文件保存失败");
      return false;
    }
  }

  /**
   * 在文件夹中显示文件
   */
  showInFolder(filePath: string): void {
    utools.shellShowItemInFolder(filePath);
  }

  /**
   * 用默认程序打开文件
   */
  openFile(filePath: string): void {
    utools.shellOpenPath(filePath);
  }
}
