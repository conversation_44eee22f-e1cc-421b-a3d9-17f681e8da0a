/* DirectoryNavigation 组件样式 - 使用命名空间避免样式冲突 */
.directory-navigation .nav-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
}

.directory-navigation .nav-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
}

.directory-navigation .paragraphs-container {
  height: calc(100% - 49px); /* 减去头部高度 */
  overflow-y: auto;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.directory-navigation .paragraphs-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.directory-navigation .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-color-muted);
}

.directory-navigation .empty-icon {
  margin-bottom: 12px;
  opacity: 0.5;
}

.directory-navigation .empty-text {
  text-align: center;
}

.directory-navigation .empty-text p {
  margin: 4px 0;
}
