# uTools 插件开发环境设置指南

## 🚀 快速开始

### 1. 安装预加载脚本依赖

在 `public/preload` 目录中安装 TypeScript 相关依赖：

```bash
cd public/preload
pnpm install -D typescript @types/node utools-api-types rimraf
```

### 2. 验证构建流程

```bash
# 回到项目根目录
cd ../..

# 构建预加载脚本
pnpm run build:preload

# 完整构建
pnpm run build
```

### 3. 开发模式

```bash
# 启动 Vue 应用开发服务器（会自动构建预加载脚本）
pnpm run dev

# 或者分别启动
pnpm run dev:preload  # 监听预加载脚本变化
pnpm run dev          # 启动 Vue 开发服务器
```

## 📁 项目结构说明

```
├── public/
│   ├── preload/
│   │   ├── services.ts      # 预加载脚本源码（简化版）
│   │   ├── services.js      # 编译后的脚本
│   │   ├── modules/         # 功能模块（可选）
│   │   ├── package.json     # 预加载脚本依赖
│   │   └── tsconfig.json    # TypeScript 配置
│   └── plugin.json          # uTools 插件配置
├── src/                     # Vue 应用源码
├── scripts/
│   └── build-preload.js     # 预加载脚本构建工具
└── package.json             # 主项目配置
```

## 🔧 预加载脚本开发

### 当前的简化版本

`public/preload/services.ts` 保持简单：

```typescript
// 插件进入事件处理
utools.onPluginEnter(({ code, type, payload }) => {
  console.log("插件进入:", { code, type, payload });
  
  // 设置窗口高度
  utools.setExpendHeight(600);
  
  // 将数据传递给渲染进程
  if (payload) {
    (window as any).utoolsPayload = { code, type, payload };
  }
});

// 为渲染进程提供简单的 API
(window as any).utoolsAPI = {
  showNotification: (message: string) => utools.showNotification(message),
  hideWindow: () => utools.hideMainWindow(),
  openExternal: (url: string) => utools.shellOpenExternal(url),
  copyText: (text: string) => utools.copyText(text)
};
```

### 在 Vue 组件中使用

```vue
<script setup lang="ts">
import { onMounted } from 'vue';

onMounted(() => {
  // 检查是否在 uTools 环境中
  if ((window as any).utoolsAPI) {
    console.log('运行在 uTools 环境');
    
    // 获取传入的数据
    const payload = (window as any).utoolsPayload;
    if (payload) {
      console.log('接收到数据:', payload);
    }
  } else {
    console.log('运行在浏览器环境');
  }
});

// 使用 uTools API
const showMessage = () => {
  if ((window as any).utoolsAPI) {
    (window as any).utoolsAPI.showNotification('Hello from Vue!');
  }
};
</script>
```

## 📋 常用命令

```bash
# 安装依赖
pnpm install

# 开发模式
pnpm run dev

# 构建
pnpm run build

# 只构建预加载脚本
pnpm run build:preload

# 监听预加载脚本变化
pnpm run dev:preload

# 类型检查
pnpm run type-check

# 验证插件配置
pnpm run validate-plugin
```

## 🐛 常见问题

### 1. TypeScript 编译错误
确保已安装依赖：
```bash
cd public/preload
pnpm install
```

### 2. uTools API 类型错误
确保安装了 `utools-api-types`：
```bash
pnpm add -D utools-api-types
```

### 3. 预加载脚本不生效
检查 `plugin.json` 中的 `preload` 路径是否正确：
```json
{
  "preload": "preload/services.js"
}
```

### 4. 构建失败
清理并重新构建：
```bash
cd public/preload
pnpm run clean
pnpm run build
```

## 🎯 下一步

1. **简单项目**：直接使用当前的简化版预加载脚本
2. **复杂项目**：参考 `docs/preload-multi-files.md` 添加功能模块
3. **高级功能**：参考 `docs/utools-development.md` 了解更多 uTools API

现在您可以开始开发 uTools 插件了！🎉
