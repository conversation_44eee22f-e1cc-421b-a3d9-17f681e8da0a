import { getMainData } from "@/services/database/mainDataService";
import { getArticles } from "@/services/database/rssContentService";
import { MainDataDocument } from "@/types/rss/main-data";
import { ArticleListResponse, RSSContentItem } from "@/types/rss/rss-content";
import { ref, computed } from "vue";

export function useRSSDataManager() {
  // 状态
  const mainData = ref<MainDataDocument | null>(null);
  const articles = ref<RSSContentItem[]>([]);
  const loading = ref(false);
  const currentSourceId = ref<string | null>(null);
  const currentSourceType = ref<"subscription" | "folder" | null>(null);

  // 计算属性
  const subscriptionCount = computed(() => {
    if (!mainData.value) return 0;

    const countSubscriptions = (nodes: Array<any>): number => {
      return nodes.reduce((count, node) => {
        if (node.type === "subscription") {
          return count + 1;
        } else if (node.type === "folder") {
          return count + countSubscriptions(node.children);
        }
        return count;
      }, 0);
    };

    return countSubscriptions(mainData.value.children);
  });

  // 获取文章总数
  const totalArticles = computed(() => articles.value.length);

  // 获取所有数据
  const fetchAllData = async () => {
    loading.value = true;
    try {
      const [mainDataResult] = await Promise.all([getMainData()]);
      mainData.value = mainDataResult;

      // 获取所有文章
      await fetchArticles();
    } finally {
      loading.value = false;
    }
  };

  // 获取指定源的文章
  const fetchArticles = async (
    sourceId?: string,
    type?: "subscription" | "folder"
  ) => {
    loading.value = true;
    try {
      // 更新当前源信息
      if (sourceId !== undefined) currentSourceId.value = sourceId;
      if (type !== undefined) currentSourceType.value = type;

      // 获取文章
      const articlesResult: ArticleListResponse = await getArticles(
        currentSourceId.value || undefined,
        currentSourceType.value || undefined
      );

      articles.value = articlesResult.items;
    } finally {
      loading.value = false;
    }
  };

  // 切换到所有文章视图
  const switchToAllArticles = async () => {
    currentSourceId.value = null;
    currentSourceType.value = null;
    await fetchArticles();
  };

  // 切换到特定订阅源
  const switchToSubscription = async (subscriptionId: string) => {
    currentSourceId.value = subscriptionId;
    currentSourceType.value = "subscription";
    await fetchArticles();
  };

  // 切换到特定文件夹
  const switchToFolder = async (folderId: string) => {
    currentSourceId.value = folderId;
    currentSourceType.value = "folder";
    await fetchArticles();
  };

  // 刷新当前数据
  const refreshCurrentData = async () => {
    // 总是先获取最新的主数据
    mainData.value = await getMainData();

    if (currentSourceId.value && currentSourceType.value) {
      await fetchArticles(currentSourceId.value, currentSourceType.value);
    } else {
      await fetchArticles();
    }
  };

  return {
    // 状态
    mainData,
    articles,
    loading,
    currentSourceId,
    currentSourceType,

    // 计算属性
    subscriptionCount,
    totalArticles,

    // 操作
    fetchAllData,
    fetchArticles,
    switchToAllArticles,
    switchToSubscription,
    switchToFolder,
    refreshCurrentData,
  };
}
