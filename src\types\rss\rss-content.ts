export enum SystemTag {
  READ = "已读",
  BOOKMARK = "书签",
}

export enum TagType {
  SYSTEM = "system",
  CUSTOM = "custom",
}

// 标签前缀映射（内部使用，不对外暴露）
export const TAG_PREFIXES = {
  [TagType.SYSTEM]: "@",
  [TagType.CUSTOM]: "#",
} as const;

export interface RSSContentDocument {
  _id: string; // 格式: "rss-browser/content/{source-id}"
  _rev?: string; // 版本号
  sourceId: string; // 订阅源 ID
  sourceTitle: string; // 订阅源标题
  lastUpdated: string; // 最后更新时间
  items: RSSContentItem[]; // 文章列表
  lastFetchTime: string; // 最后获取时间
  fetchCount: number; // 获取次数
}

export interface RSSContentItem {
  id: string; // 文章 ID
  title: string; // 文章标题
  link: string; // 文章链接
  content?: string; // 文章内容（可截断）
  contentSnippet?: string; // 内容摘要
  pubDate: string; // 发布日期
  creator?: string; // 作者
  categories?: string[]; // 分类
  addedDate: string; // 添加日期
  lastAccessedDate?: string; // 最后访问日期
  sourceId: string; // 订阅源 ID

  // 简化的标记系统
  marks: string[]; // 标记数组，如 ["已读", "书签", "重要"]
}

// 统一的文章列表返回格式
export interface ArticleListResponse {
  items: RSSContentItem[]; // 文章列表
  totalCount: number; // 总文章数
}
