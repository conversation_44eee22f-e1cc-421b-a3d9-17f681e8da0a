// 全局类型定义
declare global {
  interface Window {
    utools: typeof utools;
    services: {
      readFile: (file: string) => string;
      writeTextFile: (text: string) => string;
      writeImageFile: (base64Url: string) => string | undefined;
    };
  }
}

// utools 插件进入参数类型
export interface PluginEnterAction {
  code: string;
  type: string;
  payload: string | object;
}

// 路由元信息类型
export interface RouteMetaInfo {
  title: string;
  utoolsCode: string;
}

// 扩展 Vue Router 的 RouteMeta 类型
declare module "vue-router" {
  interface RouteMeta extends RouteMetaInfo {}
}

// Pinia 相关类型定义
export interface CounterState {
  count: number;
  name: string;
  history: number[];
}

export interface UserState {
  name: string;
  email: string;
  isLoggedIn: boolean;
  preferences: {
    theme: "light" | "dark";
    language: string;
  };
}

export {};
