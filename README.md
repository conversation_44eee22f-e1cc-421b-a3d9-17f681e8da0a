# utools RSS 浏览器插件 (TypeScript + Vue Router + Pinia + UnoCSS 版本)

这是一个基于 Vue 3 + TypeScript + Vue Router + Pinia + UnoCSS + Vite 的现代化 utools 插件项目，采用 Obsidian 风格的深色主题设计，集成了完整的设计令牌系统。

## 🚀 快速开始

### 安装依赖

```bash
# 安装主项目依赖
pnpm install

# 安装预加载脚本依赖
cd public/preload
pnpm install -D typescript @types/node utools-api-types rimraf
cd ../..
```

### 开发模式

```bash
pnpm run dev
```

### 构建生产版本

```bash
pnpm run build
```

📋 **详细指南**：

- [设置指南](docs/setup-guide.md) - 快速开始和环境配置
- [uTools 开发指南](docs/utools-development.md) - Node.js 脚本和 Electron 集成
- [多文件管理](docs/preload-multi-files.md) - 预加载脚本的模块化开发

## 项目结构

```
├── src/
│   ├── components/
│   │   ├── Layout/              # 布局组件目录
│   │   │   ├── Layout.vue       # 布局组件
│   │   │   ├── Layout.css       # 布局样式
│   │   │   └── index.ts         # 导出文件
│   │   └── Navigation/          # 导航组件目录
│   │       ├── Navigation.vue   # 导航组件
│   │       ├── Navigation.css   # 导航样式
│   │       └── index.ts         # 导出文件
│   ├── router/
│   │   └── index.ts             # Vue Router 配置
│   ├── stores/
│   │   └── counter.ts           # Pinia 状态管理 stores
│   ├── styles/                  # 样式文件目录
│   │   ├── index.css            # 主样式文件
│   │   ├── global.css           # 全局样式
│   │   ├── components/          # 集中管理的组件样式
│   │   │   ├── hello.css        # Hello 组件样式
│   │   │   └── pinia.css        # Pinia 页面样式
│   │   └── pages/               # 页面样式
│   │       ├── home.css         # Home 页面样式
│   │       └── read-write.css   # Read/Write 页面样式
│   ├── types/
│   │   ├── global.d.ts          # 全局类型定义
│   │   └── vue-shim.d.ts        # Vue 文件类型声明
│   ├── views/
│   │   ├── Home.vue             # 首页组件
│   │   ├── Read.vue             # 读文件页面
│   │   ├── Write.vue            # 写文件页面
│   │   └── Pinia.vue            # Pinia 示例页面
│   ├── Hello/
│   │   └── index.vue            # Hello 组件 (已弃用)
│   ├── App.vue                  # 主应用组件 (TypeScript)
│   ├── main.ts                  # 应用入口文件 (TypeScript)
│   └── main.css                 # 样式文件
├── public/
│   ├── preload/
│   │   ├── services.ts          # preload 脚本源文件 (TypeScript)
│   │   ├── services.js          # 编译后的 preload 脚本
│   │   ├── tsconfig.json        # preload TypeScript 配置
│   │   └── package.json         # preload 构建配置
│   ├── logo.png
│   └── plugin.json              # utools 插件配置
├── tsconfig.json                # 主项目 TypeScript 配置
├── tsconfig.node.json           # Node.js 环境 TypeScript 配置
├── vite.config.ts               # Vite 配置文件 (TypeScript)
└── package.json                 # 项目依赖和脚本
```

## 项目特性

### 1. TypeScript 支持

- 完整的 TypeScript 类型定义
- 严格模式类型检查
- Vue 组件的类型安全

### 2. Vue Router 集成

- 基于 Hash 模式的路由系统
- 与 utools 插件系统完美集成
- 支持路由守卫和导航

### 3. Pinia 状态管理

- Vue 官方推荐的状态管理库
- 完整的 TypeScript 类型支持
- 支持 Composition API 和 Options API 两种风格
- 提供计数器和用户管理的示例 stores
- 包含状态持久化和历史记录功能

### 4. UnoCSS 原子化 CSS

- 高性能的原子化 CSS 引擎
- 支持 Tailwind CSS 兼容的原子类
- 自定义主题和颜色系统 (primary, vue, pinia 色系)
- 预定义的组件快捷方式 (btn, card, input 等)
- 响应式设计和深色模式支持
- **混合式样式架构**：复杂组件样式就近放置，简单组件集中管理

### 5. 依赖更新

- 添加了 `typescript`、`@types/node`、`vue-tsc` 等 TypeScript 相关依赖
- 集成了 `vue-router` 4.x 版本
- 添加了 `pinia` 状态管理库
- 集成了 `unocss` 及相关预设和转换器
- 保留了原有的 `utools-api-types` 类型定义

### 6. 配置文件

- 创建了 `tsconfig.json` 替代 `jsconfig.json`
- 创建了 `tsconfig.node.json` 用于 Node.js 环境
- 将 `vite.config.js` 转换为 `vite.config.ts`
- 创建了 `uno.config.ts` UnoCSS 配置文件

### 7. 源代码转换

- 将 `src/main.js` 转换为 `src/main.ts`
- 为 Vue 组件添加了完整的 TypeScript 类型定义
- 创建了 `src/types/global.d.ts` 全局类型定义文件
- 创建了 `src/types/vue-shim.d.ts` Vue 文件类型声明
- 重构了组件结构，采用 views/components 分离
- 集成了 Pinia 状态管理系统
- 样式系统迁移到 UnoCSS 原子化 CSS

### 8. Preload 脚本

- 将 `public/preload/services.js` 转换为 TypeScript
- 添加了完整的类型定义和接口
- 配置了独立的 TypeScript 编译环境

## Vue Router 路由配置

### 路由规则

- `/` 和 `/hello` - 首页 (对应 utools code: hello)
- `/read` - 读文件页面 (对应 utools code: read)
- `/write` - 写文件页面 (对应 utools code: write)

### utools 集成

- 自动根据 utools 插件进入的 code 导航到对应路由
- 支持插件进入参数的传递和共享
- 开发环境默认显示首页，便于调试

## 开发命令

```bash
# 启动开发服务器
pnpm run dev

# 类型检查
pnpm run type-check

# 构建项目
pnpm run build

# 构建 preload 脚本
pnpm run build:preload

# 预览构建结果
pnpm run preview

# 验证插件配置
pnpm run validate-plugin

# 查看样式迁移指南
pnpm run style-guide
```

## 类型安全特性

1. **全局类型定义**: 为 `window.utools` 和 `window.services` 提供了完整的类型定义
2. **组件类型**: Vue 组件使用了 TypeScript 的 `<script setup>` 语法和类型定义
3. **接口定义**: 为插件进入参数等定义了 `PluginEnterAction` 接口
4. **路由类型**: 扩展了 Vue Router 的 `RouteMeta` 类型，支持 utools 集成
5. **严格模式**: 启用了 TypeScript 严格模式，提供更好的类型检查

## 组件架构

### 布局系统

- `Layout.vue`: 主布局组件，包含侧边导航和内容区域
- `Navigation.vue`: 导航组件，显示路由链接和当前状态

### 页面组件

- `Home.vue`: 首页，显示插件信息和功能介绍
- `Read.vue`: 读文件页面，支持文件内容读取和显示
- `Write.vue`: 写文件页面，支持文本和图片文件保存

### 数据流

- 使用 Vue 的 `provide/inject` 在组件间共享 `enterAction` 数据
- 路由参数通过 Vue Router 自动管理
- 支持响应式的状态更新

## 构建流程

1. 主项目构建：`vue-tsc && vite build`

   - 先进行 TypeScript 类型检查
   - 然后使用 Vite 构建生产版本

2. Preload 脚本构建：`cd public/preload && pnpm run build`
   - 独立编译 preload 的 TypeScript 代码
   - 生成 CommonJS 格式的 JavaScript 文件

## 开发环境

- Node.js 22.14.0
- TypeScript 5.9.2
- Vue 3.5.13
- Vue Router 4.5.1
- Vite 6.0.11

## 使用说明

### 开发环境

1. 启动开发服务器：`pnpm run dev`
2. 访问 `http://localhost:5173` 查看应用
3. 使用左侧导航栏切换不同页面
4. 所有功能都可以在浏览器中预览和测试

### utools 环境

1. 构建项目：`pnpm run build`
2. 在 utools 中安装插件
3. 通过关键词触发不同功能：
   - "你好"、"hello"、"首页"、"主页" → 插件首页
   - "读文件"、"读取文件"、"查看文件" → 读文件功能
   - "保存为文件"、"写文件"、"导出文件" → 写文件功能
   - "pinia"、"状态管理"、"store"、"示例" → Pinia 状态管理示例

### 路由导航

- 在开发环境中，可以直接通过 URL 访问不同页面
- 在 utools 环境中，路由会根据插件进入的 code 自动切换
- 支持浏览器前进后退按钮

## 🔧 插件配置

### plugin.json 功能配置

插件支持以下功能模块，每个模块都有多个触发关键词：

| 功能代码 | 功能说明     | 触发关键词                           | 特殊匹配      |
| -------- | ------------ | ------------------------------------ | ------------- |
| `hello`  | 插件首页     | "你好"、"hello"、"首页"、"主页"      | -             |
| `read`   | 读取文件     | "读文件"、"读取文件"、"查看文件"     | 文件拖拽      |
| `write`  | 保存文件     | "保存为文件"、"写文件"、"导出文件"   | 文本/图片选中 |
| `pinia`  | 状态管理示例 | "pinia"、"状态管理"、"store"、"示例" | -             |

### 匹配类型说明

- **文件匹配** (`read`): 支持拖拽单个文件到 utools 进行读取
- **文本匹配** (`write`): 支持选中文本后右键"保存为文件"
- **图片匹配** (`write`): 支持选中图片后右键"保存图片为文件"

## 🍍 Pinia 使用示例

### 创建 Store (Composition API 风格)

```typescript
// stores/counter.ts
import { defineStore } from "pinia";
import { ref, computed } from "vue";

export const useCounterStore = defineStore("counter", () => {
  // 状态
  const count = ref(0);

  // 计算属性
  const doubleCount = computed(() => count.value * 2);

  // 方法
  function increment() {
    count.value++;
  }

  return { count, doubleCount, increment };
});
```

### 在组件中使用 Store

```vue
<template>
  <div>
    <p>计数: {{ counterStore.count }}</p>
    <p>双倍: {{ counterStore.doubleCount }}</p>
    <button @click="counterStore.increment()">+1</button>
  </div>
</template>

<script setup lang="ts">
import { useCounterStore } from "../stores/counter";

const counterStore = useCounterStore();
</script>
```

### Options API 风格的 Store

```typescript
export const useUserStore = defineStore("user", {
  state: () => ({
    name: "用户",
    isLoggedIn: false,
  }),

  getters: {
    displayName: (state) => (state.isLoggedIn ? state.name : "游客"),
  },

  actions: {
    login(name: string) {
      this.name = name;
      this.isLoggedIn = true;
    },
  },
});
```

## 🎨 UnoCSS 使用示例

### 快捷方式 (Shortcuts)

```html
<!-- 按钮样式 -->
<button class="btn">基础按钮</button>
<button class="btn-primary">主要按钮</button>
<button class="btn-success">成功按钮</button>

<!-- 卡片样式 -->
<div class="card">卡片内容</div>
<div class="card-dark">深色卡片</div>

<!-- 输入框样式 -->
<input class="input" placeholder="输入内容" />
<input class="input-dark" placeholder="深色输入框" />

<!-- 布局样式 -->
<div class="flex-center">居中布局</div>
<div class="container-center">容器居中</div>
```

### 自定义颜色主题

```html
<!-- Vue 绿色系 -->
<div class="bg-vue-500 text-white">Vue 主题色</div>

<!-- Pinia 黄色系 -->
<div class="bg-pinia-500 text-white">Pinia 主题色</div>

<!-- 主色调蓝色系 -->
<div class="bg-primary-500 text-white">主题色</div>
```

### 响应式设计

```html
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <div class="card">响应式卡片 1</div>
  <div class="card">响应式卡片 2</div>
  <div class="card">响应式卡片 3</div>
</div>
```

### UnoCSS Inspector

开发时访问 http://localhost:5173/\_\_unocss/ 查看：

- 生成的 CSS 代码
- 使用的原子类统计
- 实时样式预览

## 🏗️ CSS 架构设计

### 混合式样式组织

项目采用**混合式样式组织方案**，根据组件复杂度选择不同的样式管理方式：

#### 1. 复杂组件 - 就近原则

```
src/components/Layout/
├── Layout.vue      # 组件文件
├── Layout.css      # 组件样式
└── index.ts        # 导出文件
```

- **适用**：Layout、Navigation 等复杂组件
- **优势**：样式与逻辑就近，便于维护和团队协作

#### 2. 简单组件 - 集中管理

```
src/styles/components/
├── hello.css       # Hello 组件样式
└── pinia.css       # Pinia 页面样式
```

- **适用**：Hello、Pinia 等简单组件
- **优势**：便于全局样式管理和复用

#### 3. 样式导入策略

- **复杂组件**：在 Vue 文件中直接导入 `import './Component.css'`
- **简单组件**：通过主样式文件统一导入

详细的架构设计请参考：[CSS 架构设计文档](docs/css-architecture.md)

## 🎯 下一步计划

- [ ] 添加更多的插件功能
- [ ] 优化用户界面和交互体验
- [ ] 添加数据持久化功能
- [ ] 集成更多的 utools API
- [x] 集成 Pinia 状态管理
- [x] 集成 UnoCSS 原子化 CSS
