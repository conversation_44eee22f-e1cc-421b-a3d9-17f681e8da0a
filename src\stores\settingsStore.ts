import { defineStore } from "pinia";
import { useFontSettingsStore } from "./settings/fontSettingStore";
import { ref } from "vue";
import { useCacheManager } from "./settings/cacheManager";

export const useSettingsStore = defineStore(
  "settings",
  () => {
    // 是否自动同步
    const autoSync = ref(true); // 设置默认值

    // 缓存设置
    const autoCacheRetention = ref<boolean>(false); // 是否自动清理缓存
    const cacheRetentionTime = ref<number>(0); // 缓存保留时间（天数），0表示不清理
    const cacheCleanupMode = ref<"clear" | "delete">("delete"); // 缓存清理模式：clear-清空内容，delete-删除整个内容数据

    // 字体设置
    const { fontSize, fontSizeLabel, fontSizeClass, toggleFontSize } =
      useFontSettingsStore();

    // 缓存管理器
    const {
      cleanExpiredCache,
      clearCache,
      autoCleanCache,
      cacheProgress,
      cacheTotal,
      cacheCurrentSource,
      cacheErrors,
      isCleaningCache,
      showCacheProgressModal,
      updateCacheProgress,
      hideCacheProgressModal,
    } = useCacheManager();

    return {
      autoSync,
      fontSize,
      fontSizeLabel,
      fontSizeClass,
      toggleFontSize,

      autoCacheRetention,
      cacheRetentionTime,
      cacheCleanupMode,

      cacheProgress,
      cacheTotal,
      cacheCurrentSource,
      cacheErrors,
      isCleaningCache,
      cleanExpiredCache,
      clearCache,
      autoCleanCache,
      showCacheProgressModal,
      updateCacheProgress,
      hideCacheProgressModal,
    };
  },
  {
    persist: true, // 启用持久化
  }
);
