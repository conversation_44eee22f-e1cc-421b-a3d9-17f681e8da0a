import { computed, ref } from "vue";

export function useFontSettingsStore() {
  const fontSize = ref<"small" | "medium" | "large">("medium");

  // 计算属性
  const fontSizeLabel = computed(() => {
    const labels = { small: "小", medium: "中", large: "大" };
    return labels[fontSize.value];
  });

  // 阅读体验功能
  const toggleFontSize = () => {
    const sizes: Array<"small" | "medium" | "large"> = [
      "small",
      "medium",
      "large",
    ];
    const currentIndex = sizes.indexOf(fontSize.value);
    fontSize.value = sizes[(currentIndex + 1) % sizes.length];
  };

  // CSS类
  const fontSizeClass = computed(() => `font-size-${fontSize.value}`);

  return {
    fontSize,
    fontSizeLabel,
    fontSizeClass,
    toggleFontSize,
  };
}
