/* Layout 组件样式 - Obsidian 风格 */
.layout {
  @apply flex min-h-screen;
  background-color: #313239;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
}

.layout .sidebar {
  @apply m-10;
  height: calc(100vh - 60px);
  max-height: calc(100vh - 60px);
}

.layout .sidebar-header {
  @apply flex justify-start items-center;
}

.layout .sidebar-content {
  @apply flex-shrink-0 border-r border-gray-700;
  background-color: #27282e;
  border-radius: 10px;
  border: 1.5px solid #3f414d;
  width: 280px;
  height: 100%;
  max-height: 100%;
  overflow: auto;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.layout .sidebar-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.layout .header-button {
  @apply p-8 mb-1 mr-1;
  border-radius: 6px;
  width: fit-content;
}

.layout .header-button:hover {
  background-color: #434450;
}

.layout .header-button.is-active {
  background-color: #434450;
  box-shadow: inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.2),
    inset 0 2px 4px 0 hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.2),
    inset 0 1px 1px 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.2);
}

.layout .main-content {
  @apply flex-1 m-10;
  background-color: transparent !important;
  overflow: auto;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.layout .main-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.layout .main-content.full-width {
  @apply w-full;
}
.layout .content-header {
  @apply flex justify-between items-center;
}

.layout .button-area {
  @apply flex items-center;
}

.layout .content-wrapper {
  @apply p-8 min-w-4xl mx-auto;
  border-radius: 10px;
  background-color: #27282e;
  border: 1.5px solid #3f414d;
  height: calc(100vh - 60px);
  max-height: calc(100vh - 60px);
  overflow: auto;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.layout .content-wrapper::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout {
    @apply flex-col;
  }

  .layout .sidebar-content {
    @apply w-full h-auto border-r-0 border-b border-gray-700;
  }

  .layout .main-content {
    @apply flex-none;
  }

  .layout .content-wrapper {
    @apply p-4;
  }
}
