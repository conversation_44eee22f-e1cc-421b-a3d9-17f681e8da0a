{"name": "RSS 浏览器", "version": "1.0.0", "description": "基于 Vue 3 + TypeScript + Vue Router + Pinia 的现代化 utools 插件", "author": "Your Name", "homepage": "https://github.com/your-username/utools-rss-browser", "main": "index.html", "preload": "preload/services.js", "logo": "logo.png", "development": {"main": "http://localhost:5173"}, "features": [{"code": "home", "explain": "一个简单轻便易于使用的RSS浏览器", "cmds": ["RSS"]}, {"code": "read", "explain": "读取文件内容 - 支持文本文件和图片文件的读取", "cmds": ["读文件", "读取文件", "查看文件", {"type": "files", "fileType": "file", "maxLength": 1, "label": "读取文件内容"}]}, {"code": "write", "explain": "保存内容为文件 - 支持文本和图片的文件保存", "mainHide": true, "cmds": ["保存为文件", "写文件", "导出文件", {"type": "over", "label": "保存为文件"}, {"type": "img", "label": "保存图片为文件"}]}, {"code": "pinia", "explain": "Pinia 状态管理示例 - 演示 Vue 状态管理的使用", "cmds": ["pinia", "状态管理", "store", "示例"]}, {"code": "rss", "explain": "RSS 浏览器 - 订阅和查看 RSS 源内容", "cmds": ["rss", "RSS", "订阅", "新闻", "博客", {"type": "over", "label": "订阅 RSS 源"}]}]}