/* Obsidian 风格的确认模态框样式 */
.confirm-modal .confirm-content {
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
}

.confirm-modal .confirm-message {
  color: #e4e4e7; /* 使用 obsidian.text 颜色 */
  font-size: 1rem;
  line-height: 1.625;
  margin: 20px 0;
  font-weight: 400;
}

.confirm-modal .confirm-options {
  display: flex;
  justify-content: space-between;
  align-items: start;
  padding-top: 10px;
  border-top: 1px solid #464647; /* 使用 obsidian.border 颜色 */
  margin: 0;
}

.confirm-modal .checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s ease;
  opacity: 0.5; /* 降低不透明度，表示暂时不可用 */
}

.confirm-modal .checkbox-input {
  margin-right: 0.75rem;
  cursor: not-allowed;
  accent-color: #6366f1; /* 使用 obsidian.accent 颜色 */
}

.confirm-modal .checkbox-text {
  font-size: 0.875rem;
  color: #a1a1aa; /* 使用 obsidian.text-muted 颜色 */
  font-weight: 400;
}

.confirm-modal .confirm-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin: 0;
}

.confirm-modal .btn-cancel,
.confirm-modal .btn-confirm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border-radius: 6px;
  border: 1.5px solid;
  min-width: 80px;
  font-family: inherit;
}

.confirm-modal .btn-cancel {
  color: #e4e4e7; /* 使用 obsidian.text 颜色 */
  border-color: #464647; /* 使用 obsidian.border 颜色 */
  background-color: #434450; /* 与 Layout 中的按钮背景色保持一致 */
}

.confirm-modal .btn-cancel:hover {
  color: #e4e4e7;
  border-color: #464647;
  background-color: #4a4b58;
  transform: translateY(-1px);
  box-shadow: inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.1),
    0 2px 8px rgba(0, 0, 0, 0.2);
}

.confirm-modal .btn-cancel:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.confirm-modal .btn-confirm {
  color: white;
  border-color: #6366f1;
  background-color: #6366f1;
  box-shadow: inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.1);
}

.confirm-modal .btn-confirm:hover {
  border-color: #818cf8;
  background-color: #5855eb;
  transform: translateY(-1px);
  box-shadow: inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.2),
    0 4px 12px rgba(99, 102, 241, 0.3);
}

.confirm-modal .btn-confirm:active {
  transform: translateY(0);
  background-color: #4f46e5;
  box-shadow: inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.2),
    0 2px 6px rgba(99, 102, 241, 0.3);
}

/* 焦点状态 */
.confirm-modal .btn-cancel:focus,
.confirm-modal .btn-confirm:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2), 0 2px 8px rgba(0, 0, 0, 0.2);
}

.confirm-modal .btn-confirm:focus {
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.4),
    inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.2),
    0 4px 12px rgba(99, 102, 241, 0.3);
}

/* 禁用状态 */
.confirm-modal .btn-cancel:disabled,
.confirm-modal .btn-confirm:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  transform: none !important;
  box-shadow: none !important;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .confirm-modal .confirm-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }

  .confirm-modal .btn-cancel,
  .confirm-modal .btn-confirm {
    width: 100%;
    justify-content: center;
  }
}
