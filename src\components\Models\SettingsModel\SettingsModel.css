/* Settings Modal 样式 - 与 Layout.vue 风格保持一致 */
.settings-content {
  width: 100%;
  max-width: 600px;
  max-height: 70vh; /* 限制最大高度 */
  padding: 16px 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  color: #e5e7eb;
  overflow-y: auto; /* 启用垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
  margin-top: 10px;
}

/* Webkit 浏览器滚动条样式 */
.settings-content::-webkit-scrollbar {
  width: 6px;
}

.settings-content::-webkit-scrollbar-track {
  background: #27282e;
  border-radius: 3px;
}

.settings-content::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.settings-content::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 设置分组 */
.settings-section {
  margin-bottom: 24px;
}

.settings-section:first-of-type {
  margin-top: 4px;
}

.settings-section:last-of-type {
  margin-bottom: 12px;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #e5e7eb;
  margin: 0 0 12px 0;
  padding-bottom: 6px;
  border-bottom: 1px solid #3f414d;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 分组标题图标样式 */
.section-title > div[class*="i-material-symbols"] {
  width: 18px;
  height: 18px;
  color: #9ca3af;
  flex-shrink: 0;
}

/* 设置项 - 紧凑布局 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #374151;
  transition: all 0.2s ease-in-out;
}

.setting-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.setting-item:hover {
  background-color: rgba(75, 85, 99, 0.1);
  border-radius: 4px;
  padding: 12px 8px;
}

.setting-label {
  font-weight: 500;
  color: #e5e7eb;
  font-size: 0.9rem;
  margin: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.setting-control {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.setting-description {
  font-size: 0.8rem;
  color: #9ca3af;
  font-weight: 400;
}

/* 下拉选择框样式 */
.setting-select {
  padding: 6px 10px;
  background-color: #27282e;
  border: 1px solid #374151;
  border-radius: 4px;
  color: #e5e7eb;
  font-size: 0.85rem;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-width: 100px;
}

.setting-select:hover {
  border-color: #4b5563;
}

.setting-select:focus {
  outline: none;
  border-color: #656fac;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.setting-select option {
  background-color: #27282e;
  color: #e5e7eb;
}

/* 主题按钮组 */
.theme-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.theme-btn {
  padding: 6px 12px;
  background-color: #434450;
  border: 1px solid #464647;
  border-radius: 4px;
  color: #e4e4e7;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  font-family: inherit;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 主题按钮图标样式 */
.theme-btn > div[class*="i-material-symbols"] {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.theme-btn:hover {
  background-color: #4a4b58;
  border-color: #5a5b68;
}

.theme-btn.active {
  background-color: #656fac;
  border-color: #6366f1;
  color: white;
  box-shadow: inset 0 0 0 1px hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.2),
    inset 0 2px 4px 0 hsla(232, calc(0.4 * 40%), calc(0.9 * 60%), 0.2);
}

/* 切换开关样式 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #374151;
  transition: 0.3s;
  border-radius: 20px;
  border: 1px solid #4b5563;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 2px;
  bottom: 2px;
  background-color: #9ca3af;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #6366f1;
  border-color: #6366f1;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
  background-color: white;
}

.toggle-slider:hover {
  border-color: #5a5b68;
}

input:checked + .toggle-slider:hover {
  background-color: #5856eb;
  border-color: #5856eb;
}

/* 操作按钮样式 */
.action-btn {
  padding: 8px 16px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border-radius: 4px;
  border: 1px solid;
  font-family: inherit;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  min-width: fit-content;
}

/* 操作按钮图标样式 */
.action-btn > div[class*="i-material-symbols"] {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.action-btn.primary {
  background-color: #656fac;
  border-color: #6366f1;
  color: white;
}

.action-btn.primary:hover {
  background-color: #5856eb;
  border-color: #5856eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.action-btn.secondary {
  background-color: #434450;
  border-color: #464647;
  color: #e4e4e7;
}

.action-btn.secondary:hover {
  background-color: #4a4b58;
  border-color: #5a5b68;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-btn:active {
  transform: translateY(0);
}

/* 设置操作区域 */
.settings-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 16px 0 6px 0;
  border-top: 1px solid #3f414d;
  margin-top: 16px;
  flex-wrap: wrap;
  position: sticky; /* 让操作按钮在滚动时保持可见 */
  bottom: 0;
  background: linear-gradient(
    transparent,
    #2d3748 20%
  ); /* 渐变背景，提供视觉分离 */
  backdrop-filter: blur(4px); /* 轻微模糊效果 */
}

/* 响应式设计 */
@media (max-width: 640px) {
  .settings-content {
    max-width: 100%;
    max-height: 80vh; /* 移动端增加可视高度 */
    padding: 12px 16px;
  }

  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 10px 0;
  }

  .setting-control {
    width: 100%;
    justify-content: flex-end;
  }

  .theme-buttons {
    justify-content: flex-start;
    width: 100%;
  }

  .theme-btn {
    flex: 1;
    min-width: 70px;
    justify-content: center;
  }

  .settings-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .action-btn {
    justify-content: center;
  }
}
