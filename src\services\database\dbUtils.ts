/**
 * 带重试机制的通用文档保存函数
 * 自动处理版本冲突问题，确保并发操作的安全性
 *
 * @param doc 要保存的文档对象
 * @param maxRetries 最大重试次数，默认为3
 * @param delay 重试延迟基数（毫秒），默认为1000
 * @returns Promise<保存后的文档对象>
 */
export async function saveDocumentWithRetry<
  T extends { _id: string; _rev?: string }
>(doc: T, maxRetries: number = 3, delay: number = 1000): Promise<T> {
  let lastError: any;

  for (let i = 0; i < maxRetries; i++) {
    try {
      // 尝试保存文档
      const result = await utools.db.put(doc);

      if (!result.ok) {
        // 检查是否是版本冲突错误
        if (
          (result.message && result.message.includes("conflict")) ||
          (result.message && result.message.includes("update conflict"))
        ) {
          // 如果是最后一次重试，抛出错误
          if (i === maxRetries - 1) {
            console.error(
              `文档保存失败，已达到最大重试次数 (${maxRetries}):`,
              result.message
            );
            throw new Error(
              `文档保存失败，请重试: ${result.message || "版本冲突"}`
            );
          }

          // 获取最新版本的文档
          try {
            console.log(`检测到版本冲突，正在重试 (${i + 1}/${maxRetries})...`);
            const latestDoc = await utools.db.get(doc._id);

            // 检查文档是否存在
            if (latestDoc && latestDoc._rev) {
              // 更新文档版本号
              doc._rev = latestDoc._rev;
            }

            // 等待一段时间后重试（使用指数退避算法）
            const waitTime = delay * Math.pow(2, i);
            await new Promise((resolve) => setTimeout(resolve, waitTime));

            // 继续下一次循环重试
            continue;
          } catch (getError) {
            // 如果获取文档失败，继续使用原始文档重试
            console.warn("获取最新文档版本失败，使用原始文档重试:", getError);

            // 等待一段时间后重试
            const waitTime = delay * Math.pow(2, i);
            await new Promise((resolve) => setTimeout(resolve, waitTime));

            // 继续下一次循环重试
            continue;
          }
        } else {
          // 如果不是版本冲突错误，直接抛出
          throw new Error(`文档保存失败: ${result.message || "未知错误"}`);
        }
      }

      // 更新文档的版本号
      const savedDoc = { ...doc };
      savedDoc._rev = result.rev;

      return savedDoc;
    } catch (error: any) {
      lastError = error;

      // 如果不是409冲突错误，直接抛出
      if (
        error.status !== 409 &&
        !error.message?.includes("conflict") &&
        !error.message?.includes("update conflict")
      ) {
        throw error;
      }

      // 如果是最后一次重试，抛出错误
      if (i === maxRetries - 1) {
        console.error(
          `文档保存失败，已达到最大重试次数 (${maxRetries}):`,
          error
        );
        throw new Error(`文档保存失败，请重试: ${error.message || "版本冲突"}`);
      }

      // 获取最新版本的文档
      try {
        console.log(`检测到版本冲突，正在重试 (${i + 1}/${maxRetries})...`);
        const latestDoc = await utools.db.get(doc._id);

        // 检查文档是否存在
        if (latestDoc && latestDoc._rev) {
          // 更新文档版本号
          doc._rev = latestDoc._rev;
        }

        // 等待一段时间后重试（使用指数退避算法）
        const waitTime = delay * Math.pow(2, i);
        await new Promise((resolve) => setTimeout(resolve, waitTime));
      } catch (getError) {
        // 如果获取文档失败，继续使用原始文档重试
        console.warn("获取最新文档版本失败，使用原始文档重试:", getError);
      }
    }
  }

  throw lastError;
}

/**
 * 安全更新文档的通用函数
 * 先获取最新版本，然后应用更新，最后保存
 *
 * @param docId 文档ID
 * @param updateFn 更新函数，接收最新文档作为参数，返回更新后的文档
 * @param maxRetries 最大重试次数，默认为3
 * @returns Promise<更新后的文档对象>
 */
export async function updateDocumentSafely<
  T extends { _id: string; _rev?: string }
>(
  docId: string,
  updateFn: (latestDoc: T) => T | Promise<T>,
  maxRetries: number = 3
): Promise<T> {
  let lastError: any;

  for (let i = 0; i < maxRetries; i++) {
    try {
      // 获取最新版本的文档
      const latestDoc = (await utools.db.get(docId)) as T | null;

      // 检查文档是否存在
      if (!latestDoc) {
        throw new Error(`文档不存在: ${docId}`);
      }

      // 应用更新
      const updatedDoc = await updateFn(latestDoc);

      // 保存更新后的文档
      const result = await utools.db.put(updatedDoc);

      if (!result.ok) {
        // 检查是否是版本冲突错误
        if (
          (result.message && result.message.includes("conflict")) ||
          (result.message && result.message.includes("update conflict"))
        ) {
          // 如果是最后一次重试，抛出错误
          if (i === maxRetries - 1) {
            console.error(
              `文档更新失败，已达到最大重试次数 (${maxRetries}):`,
              result.message
            );
            throw new Error(
              `文档更新失败，请重试: ${result.message || "版本冲突"}`
            );
          }

          // 等待一段时间后重试（使用指数退避算法）
          const waitTime = 1000 * Math.pow(2, i);
          console.log(
            `检测到版本冲突，正在重试 (${
              i + 1
            }/${maxRetries})，等待 ${waitTime}ms...`
          );
          await new Promise((resolve) => setTimeout(resolve, waitTime));

          // 继续下一次循环重试
          continue;
        } else {
          // 如果不是版本冲突错误，直接抛出
          throw new Error(`文档更新失败: ${result.message || "未知错误"}`);
        }
      }

      // 更新文档的版本号
      updatedDoc._rev = result.rev;

      return updatedDoc;
    } catch (error: any) {
      lastError = error;

      // 如果不是409冲突错误，直接抛出
      if (
        error.status !== 409 &&
        !error.message?.includes("conflict") &&
        !error.message?.includes("update conflict")
      ) {
        throw error;
      }

      // 如果是最后一次重试，抛出错误
      if (i === maxRetries - 1) {
        console.error(
          `文档更新失败，已达到最大重试次数 (${maxRetries}):`,
          error
        );
        throw new Error(`文档更新失败，请重试: ${error.message || "版本冲突"}`);
      }

      // 等待一段时间后重试（使用指数退避算法）
      const waitTime = 1000 * Math.pow(2, i);
      console.log(
        `检测到版本冲突，正在重试 (${
          i + 1
        }/${maxRetries})，等待 ${waitTime}ms...`
      );
      await new Promise((resolve) => setTimeout(resolve, waitTime));
    }
  }

  throw lastError;
}

/**
 * 批量安全更新文档的通用函数
 * 适用于需要同时更新多个文档的场景
 *
 * @param docs 要更新的文档数组
 * @param maxRetries 每个文档的最大重试次数，默认为3
 * @returns Promise<更新后的文档数组>
 */
export async function batchUpdateDocumentsSafely<
  T extends { _id: string; _rev?: string }
>(docs: T[], maxRetries: number = 3): Promise<T[]> {
  const results: T[] = [];

  // 顺序处理每个文档，确保更新顺序
  for (const doc of docs) {
    try {
      const updatedDoc = await saveDocumentWithRetry(doc, maxRetries);
      results.push(updatedDoc);
    } catch (error) {
      console.error(`批量更新文档失败 (${doc._id}):`, error);
      // 收集错误，继续处理其他文档
      results.push({ ...doc, _error: error } as any);
    }
  }

  return results;
}
