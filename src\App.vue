<script lang="ts" setup>
import { onMounted, ref, provide } from "vue";
import { useRouter } from "vue-router";
import { navigateToUtoolsCode } from "./router";
import type { PluginEnterAction } from "./types/global";

const router = useRouter();
const enterAction = ref<PluginEnterAction>({} as PluginEnterAction);

// 提供 enterAction 给子组件使用
provide("enterAction", enterAction);

onMounted(() => {
  // 检查是否在 utools 环境中
  if (window.utools) {
    window.utools.onPluginEnter((action: PluginEnterAction) => {
      enterAction.value = action;
      // 根据 utools 的 code 导航到对应路由
      navigateToUtoolsCode(action.code);
    });
    window.utools.onPluginOut(() => {
      // 插件退出时清空数据
      enterAction.value = {} as PluginEnterAction;
    });
  } else {
    // 开发环境默认显示 hello 页面
    enterAction.value = {
      code: "home",
      type: "text",
      payload: "开发环境测试",
    };
    // 开发环境默认导航到首页
    router.push("/");
  }
});
</script>

<template>
  <div id="app">
    <!-- 路由视图 -->
    <router-view />
  </div>
</template>

<style>
#app {
  min-height: 100vh;
}
</style>
