<template>
  <SimpleModal :show="show" title="重命名" @close="handleClose">
    <div class="rename-modal-content">
      <div class="rename-input-group">
        <input
          id="rename-input"
          v-model="newName"
          type="text"
          class="rename-input"
          :placeholder="`请输入${
            itemType === 'folder' ? '文件夹' : '订阅源'
          }名称`"
          @keyup.enter="handleConfirm"
          @keyup.esc="handleClose"
          autocomplete="off"
          spellcheck="false"
        />
      </div>
      <div class="rename-actions">
        <button class="btn-cancel" @click="handleClose" type="button">
          取消
        </button>
        <button
          class="btn-confirm"
          @click="handleConfirm"
          type="button"
          :disabled="!newName.trim()"
        >
          确定
        </button>
      </div>
    </div>
  </SimpleModal>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import SimpleModal from "../SimpleModal/SimpleModal.vue";

// 定义组件的属性
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentName: {
    type: String,
    default: "",
  },
  itemType: {
    type: String,
    default: "folder",
    validator: (value: string) => ["folder", "subscription"].includes(value),
  },
});

// 定义组件可以触发的事件
const emit = defineEmits(["close", "confirm"]);

// 响应式数据
const newName = ref(props.currentName);

// 监听currentName变化，更新输入框的值
watch(
  () => props.currentName,
  (newValue) => {
    newName.value = newValue;
  }
);

// 监听show变化，如果模态框打开则聚焦输入框
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      // 下一个DOM更新周期后聚焦输入框
      setTimeout(() => {
        const inputElement = document.getElementById("rename-input");
        if (inputElement) {
          inputElement.focus();
          // 选中所有文本
          // inputElement.select();
        }
      }, 100);
    }
  }
);

// 关闭模态框
const handleClose = () => {
  emit("close");
};

// 确认重命名
const handleConfirm = () => {
  if (newName.value.trim()) {
    emit("confirm", newName.value.trim());
  }
};
</script>

<style scoped src="./RenameModel.css"></style>
