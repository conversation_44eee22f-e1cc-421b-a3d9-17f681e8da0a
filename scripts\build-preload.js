#!/usr/bin/env node

/**
 * uTools 预加载脚本构建工具
 *
 * 功能：
 * 1. 编译 TypeScript 预加载脚本
 * 2. 复制编译后的文件到正确位置
 * 3. 清理临时文件
 */

import { execSync } from "child_process";
import { copyFileSync, existsSync, mkdirSync, rmSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, "..");
const preloadDir = join(rootDir, "public", "preload");
const distDir = join(rootDir, "dist");
const publicDistDir = join(rootDir, "public");

console.log("🔧 开始构建 uTools 预加载脚本...");

try {
  // 1. 编译 TypeScript
  console.log("📦 编译 TypeScript 预加载脚本...");
  process.chdir(preloadDir);
  execSync("pnpm run build", { stdio: "inherit" });

  // 2. 确保目标目录存在
  const targetPreloadDir = join(publicDistDir, "preload");
  if (!existsSync(targetPreloadDir)) {
    mkdirSync(targetPreloadDir, { recursive: true });
  }

  // 3. 检查编译后的文件（现在直接输出到 public/preload 目录）
  const sourceFile = join(preloadDir, "services.js");

  if (existsSync(sourceFile)) {
    console.log("✅ 预加载脚本编译完成:", sourceFile);
  } else {
    throw new Error(`编译后的文件不存在: ${sourceFile}`);
  }

  console.log("✨ 预加载脚本构建完成！");
} catch (error) {
  console.error("❌ 构建失败:", error.message);
  process.exit(1);
} finally {
  // 恢复工作目录
  process.chdir(rootDir);
}
