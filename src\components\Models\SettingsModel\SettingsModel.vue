<template>
  <SimpleModal :show="show" title="设置" @close="handleClose">
    <div class="settings-content">
      <!-- 阅读体验设置 -->
      <div class="settings-section">
        <h3 class="section-title">
          <div class="i-material-symbols-book-outline" />
          阅读体验
        </h3>

        <!-- 字体大小设置 -->
        <div class="setting-item">
          <label class="setting-label">字体大小</label>
          <div class="setting-control">
            <select
              v-model="settingsStore.fontSize"
              class="setting-select"
              @change="handleFontSizeChange"
            >
              <option value="small">小</option>
              <option value="medium">中</option>
              <option value="large">大</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 功能设置 -->
      <div class="settings-section">
        <h3 class="section-title">
          <div class="i-material-symbols-settings-outline" />
          功能设置
        </h3>

        <!-- 同步设置 -->
        <div class="setting-item">
          <label class="setting-label">
            自动同步
            <span class="setting-description"
              >打开插件的时候进行同步RSS源操作</span
            >
          </label>
          <div class="setting-control">
            <label class="toggle-switch">
              <input
                type="checkbox"
                v-model="showNotifications"
                @change="handleautoSyncChange"
              />
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>

      <!-- 数据管理 -->
      <div class="settings-section">
        <h3 class="section-title">
          <div class="i-material-symbols-database-outline" />
          数据管理
        </h3>

        <!-- 自动缓存清理设置 -->
        <div class="setting-item">
          <label class="setting-label">
            自动缓存清理
            <span class="setting-description"
              >启用后每次启动插件自动清理缓存</span
            >
          </label>
          <div class="setting-control">
            <label class="toggle-switch">
              <input
                type="checkbox"
                v-model="autoCacheRetention"
                @change="handleAutoCacheRetentionChange"
              />
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>

        <!-- 缓存保留时间设置 -->
        <div class="setting-item">
          <label class="setting-label">缓存保留时间</label>
          <div class="setting-control">
            <select
              v-model.number="cacheRetentionTime"
              class="setting-select"
              @change="handleCacheRetentionTimeChange"
            >
              <option value="1">1天</option>
              <option value="7">7天</option>
              <option value="30">1个月</option>
              <option value="90">3个月</option>
              <option value="180">6个月</option>
              <option value="365">1年</option>
              <option value="0">永久</option>
            </select>
          </div>
        </div>

        <div class="setting-item">
          <label class="setting-label">缓存清理方式</label>
          <div class="setting-control">
            <select
              v-model.number="cacheCleanupMode"
              class="setting-select"
              @change="handleCacheCleanupModeChange"
            >
              <option value="clear">保留标题链接</option>
              <option value="delete">彻底删除</option>
            </select>
          </div>
        </div>

        <div class="setting-item" v-if="cacheRetentionTime !== 0">
          <label class="setting-label">
            缓存管理
            <span class="setting-description">清理已缓存的文章内容</span>
          </label>
          <div class="setting-control">
            <button class="action-btn secondary" @click="clearCache">
              <div class="i-material-symbols-delete-outline" />
              清理缓存
            </button>
          </div>
        </div>

        <div class="setting-item">
          <label class="setting-label">
            数据导出
            <span class="setting-description">导出订阅源和书签数据</span>
          </label>
          <div class="setting-control">
            <button class="action-btn secondary" @click="exportData">
              <div class="i-material-symbols-file-export-outline-rounded" />
              导出数据
            </button>
          </div>
        </div>
      </div>
    </div>
  </SimpleModal>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useSettingsStore } from "@/stores/settingsStore";
import SimpleModal from "../SimpleModal/SimpleModal.vue";
import { useRSSStore } from "@/stores/rssStore";

const settingsStore = useSettingsStore();
const rssStore = useRSSStore();

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["close"]);

// 本地设置状态
const autoCacheRetention = ref(false);
const cacheRetentionTime = ref(0);
const showNotifications = ref(true);
const cacheCleanupMode = ref<"clear" | "delete">("delete");

// 设置处理方法
const handleFontSizeChange = () => {
  console.log("字体大小已更改为:", settingsStore.fontSizeLabel);
};

const handleCacheCleanupModeChange = () => {
  // 更新 store 中的值
  settingsStore.cacheCleanupMode = cacheCleanupMode.value;
  console.log("缓存清理模式设置:", cacheCleanupMode.value);
};

const handleAutoCacheRetentionChange = () => {
  // 更新 store 中的值
  settingsStore.autoCacheRetention = autoCacheRetention.value;
  console.log("自动缓存清理设置:", autoCacheRetention.value);
};

const handleCacheRetentionTimeChange = () => {
  // 更新 store 中的值
  settingsStore.cacheRetentionTime = cacheRetentionTime.value;
  // 显示选择的保留时间
  let timeText = "从不";
  if (cacheRetentionTime.value > 0) {
    if (cacheRetentionTime.value === 1) {
      timeText = "1天";
    } else if (cacheRetentionTime.value < 30) {
      timeText = `${cacheRetentionTime.value}天`;
    } else if (cacheRetentionTime.value === 30) {
      timeText = "1个月";
    } else if (cacheRetentionTime.value === 90) {
      timeText = "3个月";
    } else if (cacheRetentionTime.value === 180) {
      timeText = "6个月";
    } else if (cacheRetentionTime.value === 365) {
      timeText = "1年";
    } else {
      timeText = `${Math.round(cacheRetentionTime.value / 30)}个月`;
    }
  }
  console.log("缓存清理时间设置:", timeText);
};

const handleautoSyncChange = () => {
  settingsStore.autoSync = showNotifications.value;
};

const clearCache = async () => {
  try {
    // 通过全局事件显示缓存清理进度模态框
    window.dispatchEvent(new Event("show-cache-modal"));

    // 使用 cacheManager 的 clearCache 方法，传入进度回调
    const result = await settingsStore.clearCache(
      cacheRetentionTime.value,
      cacheCleanupMode.value,
      (progress) => {
        // 更新进度状态
        settingsStore.updateCacheProgress(progress);
      }
    );

    // 清理完成，通过全局事件隐藏模态框
    window.dispatchEvent(new Event("hide-cache-modal"));

    // 显示结果通知
    if ((window as any).utoolsAPI) {
      (window as any).utoolsAPI.showNotification(result.message);
    } else {
      alert(result.message);
    }
  } catch (error) {
    console.error("清理缓存失败:", error);

    // 清理失败，更新状态
    settingsStore.updateCacheProgress({
      current: 0,
      total: 0,
      currentSource: "缓存清理失败",
      errors: [
        `清理失败: ${error instanceof Error ? error.message : String(error)}`,
      ],
    });

    // 清理失败，通过全局事件隐藏模态框
    window.dispatchEvent(new Event("hide-cache-modal"));

    if ((window as any).utoolsAPI) {
      (window as any).utoolsAPI.showNotification("清理缓存失败");
    } else {
      alert("清理缓存失败");
    }
  }

  // 返回首页，并刷新界面
  await rssStore.fetchAllData();
};

const exportData = () => {
  console.log("导出数据");
  // 这里可以添加导出数据的逻辑
  if ((window as any).utoolsAPI) {
    (window as any).utoolsAPI.showNotification("数据导出功能开发中");
  } else {
    alert("数据导出功能开发中");
  }
};

// 关闭模态框
const handleClose = () => {
  emit("close");
};

// 初始化设置
const initializeSettings = () => {
  // 从 store 中获取设置值
  autoCacheRetention.value = settingsStore.autoCacheRetention;
  cacheRetentionTime.value = settingsStore.cacheRetentionTime;
  showNotifications.value = settingsStore.autoSync;
  cacheCleanupMode.value = settingsStore.cacheCleanupMode;
};

onMounted(() => {
  initializeSettings();
});
</script>

<style scoped src="./SettingsModel.css"></style>
