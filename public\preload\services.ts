/**
 * uTools RSS 浏览器预加载脚本
 * 精简版本，只保留必要的功能
 */

import type { PluginEnterData } from "./types/global";
import { RSSHandler } from "./modules/rss-handler";
const rssHandler = new RSSHandler();

// 插件进入事件处理
utools.onPluginEnter(({ code, type, payload }: PluginEnterData) => {
  console.log("插件进入:", { code, type, payload });

  // 设置窗口高度
  utools.setExpendHeight(600);

  // 在home功能进入时执行同步
  if (code === "home") {
    console.log("触发同步操作");

    // 直接调用渲染进程中的全局函数
    if (typeof (window as any).performSync === "function") {
      (window as any).performSync();
    } else {
      console.log("performSync 函数不存在，等待应用加载完成");
      // 如果函数不存在，等待一段时间后重试
      setTimeout(() => {
        if (typeof (window as any).performSync === "function") {
          (window as any).performSync();
        } else {
          console.error("performSync 函数仍然不存在");
        }
      }, 1000);
    }
  }

  // RSS 功能处理
  if (code === "rss") {
    // 如果有 URL 输入，直接订阅
    if (payload && typeof payload === "string") {
      rssHandler.displayRSSContent(payload);
    } else {
      // 没有输入，跳转到 RSS 页面让用户输入
      (window as any).utoolsPayload = { code, type, payload };
    }
  }
});

// 插件退出事件处理
utools.onPluginOut(() => {
  console.log("插件退出");
});

// 为渲染进程提供必要的 API
(window as any).utoolsAPI = {
  // 显示通知
  showNotification: (message: string) => {
    utools.showNotification(message);
  },

  // RSS 功能 API
  rss: {
    subscribe: (url: string) => rssHandler.subscribeRSS(url),
    display: (url: string) => rssHandler.displayRSSContent(url),
  },

  // 添加ubrowser支持
  ubrowser: utools.ubrowser,
};
