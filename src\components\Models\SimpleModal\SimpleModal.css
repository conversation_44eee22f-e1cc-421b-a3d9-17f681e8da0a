/* 遮罩层样式 */
.modal-overlay {
  /* 固定定位，覆盖整个屏幕 */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  /* 半透明黑色背景 */
  background-color: rgba(0, 0, 0, 0.5);

  /* 或者使用简单的半透明背景替代 */
  background-color: rgba(0, 0, 0, 0.7);

  /* 居中显示内容 */
  display: flex;
  justify-content: center;
  align-items: center;

  /* 动画效果 */
  animation: fadeIn 0.3s ease-out;
}

/* 模态框内容容器 */
.modal-content {
  /* 背景和圆角 */
  background: white;
  border-radius: 8px;

  /* 阴影效果 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

  /* 大小和间距 */
  max-width: 500px;
  width: 90%;
  max-height: 80vh;

  /* 相对定位，用于放置关闭按钮 */
  position: relative;

  /* 动画效果 */
  animation: slideIn 0.3s ease-out;
}

/* 关闭按钮 */
.close-btn {
  /* 绝对定位到右上角 */
  position: absolute;
  top: 10px;
  right: 15px;

  /* 按钮样式 */
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;

  /* 圆形背景 */
  width: 30px;
  height: 30px;
  border-radius: 50%;

  /* 居中显示 × */
  display: flex;
  justify-content: center;
  align-items: center;

  /* 过渡效果 */
  transition: all 0.2s ease;
}

/* 关闭按钮悬停效果 */
.close-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

/* 模态框主体内容 */
.modal-body {
  padding: 30px 20px 20px 20px;
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 滑入动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modal-content {
    background: #2d3748;
    color: white;
  }

  .close-btn:hover {
    background-color: #4a5568;
    color: #e2e8f0;
  }
}

.modal-content {
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: transform, opacity;

  /* 动画效果 */
  animation: slideIn 0.3s ease-out;
}

.head-title {
  @apply pos-absolute font-600;
  top: 13px;
  left: 15px;
  font-size: 1.2rem;
}
