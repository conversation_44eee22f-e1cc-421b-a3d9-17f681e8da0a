<template>
  <div class="dragable-directories">
    <div class="drag-area" ref="el">
      <div
        v-for="(el, index) in modelValue"
        :key="el.name"
        class="directory-item"
        :data-id="el.id"
      >
        <div
          class="nav-directory-item"
          :class="{
            selected: props.enableSelection && props.selectedItemId === el.id,
          }"
          @click="toggleFolder(el)"
          @contextmenu="showBasicMenu($event, el)"
          @dblclick="handleDblClick(el)"
        >
          <!-- 文件夹/文件/段落图标 -->
          <div
            :class="`${
              isFolder(el)
                ? el.expanded
                  ? 'i-material-symbols-folder-open-outline'
                  : 'i-material-symbols-folder-outline'
                : isSubscription(el)
                ? 'i-material-symbols-rss-feed-rounded'
                : isParentNode(el)
                ? 'i-material-symbols-list-alt-outline-rounded'
                : 'i-material-symbols-format-list-bulleted' // 段落图标
            } mr-2 ${iconColor(index)} transition-all duration-200`"
          />

          <!-- 文件/文件夹名称 -->
          <div class="select-none">{{ el.name }}</div>
        </div>

        <!-- 子目录容器 -->
        <div
          v-if="
            isFolder(el) ||
            (isParagraph(el) && el.children && el.children.length > 0)
          "
          class="children-container ml-2 overflow-hidden transition-all duration-300 ease-in-out"
          :class="{
            'max-h-0 opacity-0': !el.expanded,
            'max-h-screen opacity-100': el.expanded,
          }"
        >
          <nested-function
            v-model="el.children"
            :folder-menu-items="folderMenuItems"
            :subscription-menu-items="subscriptionMenuItems"
            :paragraph-menu-items="paragraphMenuItems"
            :enable-drag="enableDrag"
            :enable-selection="enableSelection"
            :selected-item-id="selectedItemId"
            @dbl-click="handleDblClick"
            @drag-end="handleNestedDragEnd"
            @item-select="handleNestedItemSelect"
          />
        </div>
      </div>
      <!-- 右键菜单组件 -->
      <ContextMenu
        ref="contextMenuRef"
        :items="currentMenuItems"
        @item-click="handleMenuItemClick"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { SortableEvent, useDraggable } from "vue-draggable-plus";
import { computed, ref } from "vue";
import NestedFunction from "./DragableDirectories.vue";

import {
  FolderNode,
  ParagraphNode,
  SubscriptionNode,
} from "@/types/rss/main-data";
import { MenuItem } from "../ContextMenu";
import ContextMenu from "@/components/ContextMenu/ContextMenu.vue";

interface Props {
  modelValue: Array<FolderNode | SubscriptionNode | ParagraphNode> | undefined;
  folderMenuItems?: MenuItem[]; // 文件夹的菜单项（可选）
  subscriptionMenuItems?: MenuItem[]; // 订阅源的菜单项（可选）
  paragraphMenuItems?: MenuItem[]; // 段落的菜单项（可选）
  menuItems?: MenuItem[]; // 通用菜单项（可选，为了向后兼容）
  enableDrag?: boolean; // 是否启用拖拽功能，默认为 true
  enableSelection?: boolean; // 是否启用选中功能，默认为 true
  selectedItemId?: string | null; // 当前选中的项目ID
  onDblClick?: (el: FolderNode | SubscriptionNode | ParagraphNode) => void;
}

const props = withDefaults(defineProps<Props>(), {
  enableDrag: true,
  enableSelection: true,
});

// 在 Emits 接口中添加新事件
interface Emits {
  (
    e: "update:modelValue",
    value: Array<FolderNode | SubscriptionNode | ParagraphNode> | undefined
  ): void;
  (e: "drag-end"): void; // 添加拖拽结束事件
  (e: "item-select", item: FolderNode | SubscriptionNode | ParagraphNode): void; // 添加项目选中事件
}

const emits = defineEmits<Emits>();

const currentMenuItems = ref<MenuItem[]>([]);
const contextMenuRef = ref();

// 外部传入双击事件函数
// 修改 handleDblClick 函数，确保在双击时重置计数器
const handleDblClick = (el: FolderNode | SubscriptionNode | ParagraphNode) => {
  // 重置点击计数器和计时器
  clickCount = 0;
  if (clickTimer) {
    clearTimeout(clickTimer);
    clickTimer = null;
  }

  // 触发选中事件
  if (props.enableSelection) {
    console.log(
      "DragableDirectories 选中事件:",
      el.id,
      el.name,
      "当前选中ID:",
      props.selectedItemId
    );
    emits("item-select", el);
  }

  if (props.onDblClick) {
    props.onDblClick(el);
  }
};

// 基础菜单
const showBasicMenu = (
  event: MouseEvent,
  element: FolderNode | SubscriptionNode | ParagraphNode
) => {
  // 根据元素类型选择不同的菜单项
  let menuItems: MenuItem[];

  if (isFolder(element)) {
    // 优先使用文件夹特定的菜单项，如果没有则使用通用菜单项
    menuItems = props.folderMenuItems || props.menuItems || [];
  } else if (isSubscription(element)) {
    // 优先使用订阅源特定的菜单项，如果没有则使用通用菜单项
    menuItems = props.subscriptionMenuItems || props.menuItems || [];
  } else {
    // 段落类型的菜单项
    menuItems = props.paragraphMenuItems || props.menuItems || [];
  }

  // 包装 action 函数以传递 element 参数
  currentMenuItems.value = menuItems.map((item) => ({
    ...item,
    action: item.action ? () => item.action!(element) : undefined,
  }));

  contextMenuRef.value?.showMenu(event);
};

// 记录当前所有文件夹/段落的展开状态（支持嵌套）
const recordExpandedFolders = (
  nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>
): string[] => {
  const expandedFolders: string[] = [];

  const traverse = (
    items: Array<FolderNode | SubscriptionNode | ParagraphNode>
  ) => {
    items.forEach((item) => {
      if (
        (item.type === "folder" || item.type === "paragraph") &&
        item.expanded
      ) {
        expandedFolders.push(item.id);
      }
      if (
        (item.type === "folder" || item.type === "paragraph") &&
        item.children &&
        item.children.length > 0
      ) {
        traverse(item.children);
      }
    });
  };

  traverse(nodes);
  return expandedFolders;
};

// 恢复文件夹/段落的展开状态，并确保特定文件夹/段落展开（支持嵌套）
const restoreExpandedFolders = (
  nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>,
  expandedFolders: string[],
  ensureFolderIds: string[] = []
) => {
  const traverse = (
    items: Array<FolderNode | SubscriptionNode | ParagraphNode>
  ) => {
    items.forEach((item) => {
      if (item.type === "folder" || item.type === "paragraph") {
        // 如果是需要确保展开的文件夹/段落或者是之前展开的文件夹/段落，则设置为展开状态
        if (
          ensureFolderIds.includes(item.id) ||
          expandedFolders.includes(item.id)
        ) {
          item.expanded = true;
        }

        // 递归处理子文件夹/段落
        if (item.children && item.children.length > 0) {
          traverse(item.children);
        }
      }
    });
  };

  traverse(nodes);
};

// 通用函数：执行操作并保持文件夹展开状态
const executeWithFolderStatePreserved = async (
  operation: () => Promise<void>,
  ensureFolderIds: string[] = []
) => {
  if (!props.modelValue) return;

  // 记录当前所有文件夹的展开状态
  const expandedFolders = recordExpandedFolders(props.modelValue);

  try {
    // 执行操作
    await operation();

    // 等待一个事件循环，确保数据更新完成
    await new Promise((resolve) => setTimeout(resolve, 0));

    // 恢复文件夹的展开状态
    restoreExpandedFolders(props.modelValue, expandedFolders, ensureFolderIds);
  } catch (error) {
    console.error("操作失败:", error);
    throw error;
  }
};

// 暴露函数给父组件使用
defineExpose({
  executeWithFolderStatePreserved,
  recordExpandedFolders,
  restoreExpandedFolders,
});

// 处理菜单项点击
const handleMenuItemClick = (item: MenuItem) => {
  console.log("菜单项被点击:", item.label);
};

// 判断是否是文件夹
const isFolder = (item: FolderNode | SubscriptionNode | ParagraphNode) => {
  return item.type === "folder";
};

// 判断是否是父节点
const isParentNode = (item: ParagraphNode) => {
  return item.children && item.children.length > 0;
};

// 判断是否是订阅源
const isSubscription = (
  item: FolderNode | SubscriptionNode | ParagraphNode
) => {
  return item.type === "subscription";
};

// 判断是否是段落
const isParagraph = (item: FolderNode | SubscriptionNode | ParagraphNode) => {
  return item.type === "paragraph";
};

// 添加点击计数器和计时器
let clickCount = 0;
let clickTimer: number | null = null;

// 切换文件夹/段落展开/折叠状态
const toggleFolder = (item: FolderNode | SubscriptionNode | ParagraphNode) => {
  if (isFolder(item) || isParagraph(item)) {
    clickCount++;

    // 第一次点击
    if (clickCount === 1) {
      clickTimer = window.setTimeout(() => {
        // 如果在指定时间内只有一次点击，则执行单击操作
        item.expanded = !item.expanded;
        clickCount = 0;
        clickTimer = null;
      }, 200); // 200ms 用于判断是否是双击
    }
    // 第二次点击（双击）
    else if (clickCount === 2) {
      // 清除单击计时器，防止单击操作执行
      if (clickTimer) {
        clearTimeout(clickTimer);
        clickTimer = null;
      }
      clickCount = 0;
      // 双击事件会由 dblclick 处理器处理，这里不需要做任何事
    }
  }
};

// 定义文件夹的颜色
const iconColor = (level: number): string => {
  const colorLists = [
    "text-red",
    "text-orange",
    "text-yellow",
    "text-lime",
    "text-green",
    "text-emerald",
    "text-teal",
    "text-cyan",
    "text-sky",
    "text-blue",
    "text-indigo",
    "text-violet",
    "text-purple",
    "text-fuchsia",
    "text-pink",
    "text-rose",
  ];

  return level % colorLists.length === 0
    ? colorLists[0]
    : colorLists[level % colorLists.length];
};

// 处理嵌套组件的拖拽结束事件
const handleNestedDragEnd = () => {
  // 当嵌套的子组件发生拖拽结束时，也触发父组件的拖拽结束事件
  emits("drag-end");
};

// 处理嵌套组件的项目选中事件
const handleNestedItemSelect = (
  item: FolderNode | SubscriptionNode | ParagraphNode
) => {
  console.log("嵌套组件选中事件:", item.id, item.name);
  // 将嵌套组件的选中事件向上传递
  emits("item-select", item);
};

const list = computed({
  get: () => props.modelValue,
  set: (value) => emits("update:modelValue", value),
});

const el = ref();

// 动态配置拖拽选项
const dragOptions = computed(() => {
  if (!props.enableDrag) {
    return null; // 不启用拖拽
  }

  return {
    animation: 150,
    ghostClass: "ghost",
    group: "g1",
    onMove: (evt: any, originalEvent: Event) => {
      // 获取拖拽目标元素
      const targetElement = evt.to;

      // 查找最近的目录项元素
      const directoryItem = targetElement.closest(".directory-item");

      if (directoryItem instanceof HTMLElement) {
        // 获取存储在data-id属性中的项目ID
        const itemId = directoryItem.dataset.id;

        if (itemId && props.modelValue) {
          // 递归查找对应ID的项目
          const findItemById = (
            items: Array<FolderNode | SubscriptionNode | ParagraphNode>,
            id: string
          ): FolderNode | SubscriptionNode | ParagraphNode | null => {
            for (const item of items) {
              if (item.id === id) {
                return item;
              }
              if (isFolder(item) && item.children) {
                const found = findItemById(item.children, id);
                if (found) return found;
              }
            }
            return null;
          };

          const targetItem = findItemById(props.modelValue, itemId);

          // 检查是否是文件夹且处于关闭状态
          if (targetItem && isFolder(targetItem) && !targetItem.expanded) {
            // 仅展开文件夹（UI状态变化），不修改数据
            targetItem.expanded = true;
          }
        }
      }

      // 返回 true 保持默认行为
      return true;
    },
    onEnd: (event: SortableEvent) => {
      console.log("拖拽完毕", event);

      // 获取拖拽目标元素
      const targetElement = event.to;

      // 查找最近的目录项元素
      const directoryItem = targetElement.closest(".directory-item");

      if (directoryItem instanceof HTMLElement) {
        // 获取存储在data-id属性中的项目ID
        const itemId = directoryItem.dataset.id;

        if (itemId && props.modelValue) {
          // 递归查找对应ID的项目
          const findItemById = (
            items: Array<FolderNode | SubscriptionNode | ParagraphNode>,
            id: string
          ): FolderNode | SubscriptionNode | ParagraphNode | null => {
            for (const item of items) {
              if (item.id === id) {
                return item;
              }
              if (isFolder(item) && item.children) {
                const found = findItemById(item.children, id);
                if (found) return found;
              }
            }
            return null;
          };

          const targetItem = findItemById(props.modelValue, itemId);

          // 检查是否是文件夹且处于关闭状态
          if (targetItem && isFolder(targetItem) && !targetItem.expanded) {
            // 展开文件夹
            targetItem.expanded = true;
          }
        }
      }

      // 触发拖拽结束事件，由父组件处理实际的数据移动
      emits("drag-end");
    },
  };
});

// 只有在启用拖拽时才应用拖拽功能
if (dragOptions.value) {
  useDraggable(el, list, dragOptions.value);
}
</script>
<style scoped src="./DragableDirectories.css"></style>
