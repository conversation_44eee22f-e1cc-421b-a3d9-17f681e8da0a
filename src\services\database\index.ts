// src/services/database.ts
import { initializeMainData } from "./mainDataService";

// 初始化所有必要的数据结构
export async function initializeDatabase() {
  try {
    // 初始化主数据
    await initializeMainData();

    // // 初始化用户配置
    // await initializeUserConfig();

    // // 初始化会员等级表
    // await initializeMembershipTiers();

    // // 初始化统计信息
    // await initializeStats();

    console.log("所有数据结构初始化完成");
  } catch (error) {
    console.error("数据库初始化失败:", error);
    throw error;
  }
}
