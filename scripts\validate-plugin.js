#!/usr/bin/env node

/**
 * 验证 plugin.json 配置的有效性
 */

const fs = require('fs');
const path = require('path');

function validatePlugin() {
  console.log('🔍 验证 plugin.json 配置...\n');

  // 读取 plugin.json
  const pluginPath = path.join(__dirname, '../public/plugin.json');
  const distPluginPath = path.join(__dirname, '../dist/plugin.json');
  
  if (!fs.existsSync(pluginPath)) {
    console.error('❌ 找不到 public/plugin.json 文件');
    process.exit(1);
  }

  const pluginConfig = JSON.parse(fs.readFileSync(pluginPath, 'utf8'));
  
  console.log('📋 插件基本信息:');
  console.log(`   名称: ${pluginConfig.name}`);
  console.log(`   版本: ${pluginConfig.version}`);
  console.log(`   描述: ${pluginConfig.description}`);
  console.log(`   作者: ${pluginConfig.author}`);
  console.log('');

  console.log('🚀 功能配置验证:');
  
  const requiredFeatures = ['hello', 'read', 'write', 'pinia'];
  const configuredFeatures = pluginConfig.features.map(f => f.code);
  
  requiredFeatures.forEach(code => {
    if (configuredFeatures.includes(code)) {
      const feature = pluginConfig.features.find(f => f.code === code);
      console.log(`   ✅ ${code}: ${feature.explain}`);
      console.log(`      触发词: ${feature.cmds.filter(cmd => typeof cmd === 'string').join(', ')}`);
      
      const specialCmds = feature.cmds.filter(cmd => typeof cmd === 'object');
      if (specialCmds.length > 0) {
        console.log(`      特殊匹配: ${specialCmds.map(cmd => cmd.type).join(', ')}`);
      }
    } else {
      console.log(`   ❌ ${code}: 未配置`);
    }
  });

  console.log('');

  // 验证构建后的文件
  if (fs.existsSync(distPluginPath)) {
    const distConfig = JSON.parse(fs.readFileSync(distPluginPath, 'utf8'));
    const isEqual = JSON.stringify(pluginConfig) === JSON.stringify(distConfig);
    
    if (isEqual) {
      console.log('✅ dist/plugin.json 与源文件一致');
    } else {
      console.log('⚠️  dist/plugin.json 与源文件不一致，请重新构建');
    }
  } else {
    console.log('⚠️  dist/plugin.json 不存在，请运行 npm run build');
  }

  console.log('');

  // 验证必要文件
  const requiredFiles = [
    'public/index.html',
    'public/preload/services.js',
    'public/logo.png'
  ];

  console.log('📁 必要文件检查:');
  requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
      console.log(`   ✅ ${file}`);
    } else {
      console.log(`   ❌ ${file} - 文件不存在`);
    }
  });

  console.log('\n🎉 plugin.json 配置验证完成！');
}

if (require.main === module) {
  validatePlugin();
}

module.exports = { validatePlugin };
