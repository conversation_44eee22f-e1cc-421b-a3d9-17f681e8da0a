// 导入类型定义
import type {
  FolderNode,
  MainDataDocument,
  ParagraphNode,
  SubscriptionNode,
} from "@/types/rss/main-data";
import { saveDocumentWithRetry, updateDocumentSafely } from "./dbUtils";
import { deleteFolderRSSContents, deleteRSSContent } from "./rssContentService";

// 初始化主数据
export async function initializeMainData() {
  try {
    // 尝试获取现有主数据
    const mainData = await utools.db.get("rss-browser/main-data");

    // 如果存在，检查是否需要迁移
    if (mainData) {
      await migrateMainData(mainData as MainDataDocument);
      return;
    }
  } catch (error: any) {
    // 如果是 404 错误（文档不存在），则创建新的
    if (error.status !== 404) {
      throw error;
    }
  }

  // 创建新的主数据文档
  await createMainData();

  console.log("主数据文档创建成功");
}

// 数据迁移函数
export async function migrateMainData(mainData: MainDataDocument) {
  const CURRENT_VERSION = "1.0";

  // 检查版本并执行迁移
  if (
    !mainData.version ||
    parseFloat(mainData.version) < parseFloat(CURRENT_VERSION)
  ) {
    // 使用安全更新方式更新主数据
    await updateMainDataSafely(async (latestDoc) => {
      // 执行迁移逻辑
      latestDoc.version = CURRENT_VERSION;
      latestDoc.lastModifiedDate = new Date().toISOString();
      return latestDoc;
    });

    console.log("主数据迁移成功");
  }
}

/**
 * 创建主数据
 * @returns Promise<MainDataDocument>
 */
export async function createMainData(): Promise<MainDataDocument> {
  const newMainData: MainDataDocument = {
    _id: "rss-browser/main-data",
    children: [],
    lastModifiedDate: new Date().toISOString(),
    version: "1.0",
  };

  // 使用安全保存函数
  return saveDocumentWithRetry(newMainData);
}

/**
 * 获取主数据
 * @returns Promise<MainDataDocument>
 */
export async function getMainData(): Promise<MainDataDocument> {
  try {
    const doc = await utools.db.get("rss-browser/main-data");
    return doc as MainDataDocument;
  } catch (error: any) {
    if (error.status === 404) {
      // 文档不存在，创建新的
      return createMainData();
    }
    throw error;
  }
}

/**
 * 更新主数据
 * @param mainData 主数据对象
 * @returns Promise<MainDataDocument>
 */
export async function updateMainData(
  mainData: MainDataDocument
): Promise<MainDataDocument> {
  mainData.lastModifiedDate = new Date().toISOString();

  // 使用安全保存函数
  return saveDocumentWithRetry(mainData);
}

/**
 * 安全更新主数据
 * @param updateFn 更新函数
 * @returns Promise<MainDataDocument>
 */
export async function updateMainDataSafely(
  updateFn: (
    latestDoc: MainDataDocument
  ) => MainDataDocument | Promise<MainDataDocument>
): Promise<MainDataDocument> {
  return updateDocumentSafely("rss-browser/main-data", updateFn);
}

/**
 * 删除主数据
 * @returns Promise<boolean>
 */
export async function deleteMainData(): Promise<boolean> {
  try {
    // 尝试获取现有主数据
    const doc = await utools.db.get("rss-browser/main-data");

    if (doc != null) {
      // 如果存在，删除它
      const result = await utools.db.remove(doc);

      if (result.ok) {
        console.log("主数据删除成功");
        return true;
      } else {
        throw new Error(result.message || "删除主数据失败");
      }
    }
  } catch (error: any) {
    // 如果是 404 错误（文档不存在），视为删除成功
    if (error.status === 404) {
      console.log("主数据不存在，无需删除");
      return true;
    }

    console.error("删除主数据失败:", error);
    throw error;
  }
  return false;
}

/**
 * 重置主数据
 * @returns Promise<MainDataDocument>
 */
export async function resetMainData(): Promise<MainDataDocument> {
  try {
    // 先删除现有主数据
    await deleteMainData();

    // 创建新的主数据
    return await createMainData();
  } catch (error) {
    console.error("重置主数据失败:", error);
    throw error;
  }
}

/**
 * 递归查找并删除节点
 * @param nodes 节点数组
 * @param id 要删除的节点ID
 * @param type 要删除的节点类型
 * @returns boolean 是否找到并删除了节点
 */
function findAndDeleteNode(
  nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>,
  id: string,
  type: "folder" | "subscription"
): boolean {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];

    // 检查当前节点是否匹配
    if (node.id === id && node.type === type) {
      // 找到匹配节点，删除它
      nodes.splice(i, 1);
      return true;
    }

    // 如果是文件夹节点，递归检查子节点
    if (node.type === "folder") {
      const foundAndDeleted = findAndDeleteNode(node.children, id, type);
      if (foundAndDeleted) {
        return true;
      }
    }
  }

  // 未找到匹配节点
  return false;
}

/**
 * 通过ID和类型删除文件夹或订阅源
 * @param id 要删除的节点ID
 * @param type 要删除的节点类型（"folder" 或 "subscription"）
 * @returns Promise<boolean> 是否成功删除
 */
export async function deleteNodeById(
  id: string,
  type: "folder" | "subscription"
): Promise<boolean> {
  try {
    // 使用安全更新方式更新主数据
    let nodeDeleted = false;

    await updateMainDataSafely(async (mainData) => {
      // 查找并删除节点
      nodeDeleted = findAndDeleteNode(mainData.children, id, type);
      return mainData;
    });

    if (nodeDeleted) {
      // 删除对应的RSS内容
      if (type === "subscription") {
        // 删除订阅源的RSS内容
        await deleteRSSContent(id);
      } else if (type === "folder") {
        // 删除文件夹下所有订阅源的RSS内容
        await deleteFolderRSSContents(id);
      }

      console.log(`成功删除${type === "folder" ? "文件夹" : "订阅源"}: ${id}`);
      return true;
    } else {
      console.log(
        `未找到要删除的${type === "folder" ? "文件夹" : "订阅源"}: ${id}`
      );
      return false;
    }
  } catch (error) {
    console.error(`删除${type === "folder" ? "文件夹" : "订阅源"}失败:`, error);
    throw error;
  }
}

/**
 * 递归查找文件夹节点
 * @param nodes 节点数组
 * @param id 要查找的文件夹ID
 * @returns FolderNode | null 找到的文件夹节点，如果未找到则返回null
 */
function findFolderNode(
  nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>,
  id: string
): FolderNode | null {
  for (const node of nodes) {
    // 检查当前节点是否匹配
    if (node.type === "folder" && node.id === id) {
      return node;
    }

    // 如果是文件夹节点，递归检查子节点
    if (node.type === "folder") {
      const foundNode = findFolderNode(node.children, id);
      if (foundNode) {
        return foundNode;
      }
    }
  }

  // 未找到匹配节点
  return null;
}

/**
 * 生成唯一ID
 * @returns string 唯一ID
 */
function generateUniqueId(): string {
  return `folder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 创建文件夹
 * @param parentId 父文件夹ID（可选），如果未提供则在最外层创建
 * @param name 文件夹名称（可选），默认为"新建文件夹"
 * @returns Promise<FolderNode> 创建的文件夹节点
 */
export async function createFolder(
  parentId?: string,
  name: string = "新建文件夹"
): Promise<FolderNode> {
  try {
    // 创建新文件夹节点
    const newFolder: FolderNode = {
      type: "folder",
      id: generateUniqueId(),
      name,
      path: "", // 将在下面设置
      level: 0, // 将在下面设置
      children: [],
      createdDate: new Date().toISOString(),
      lastModifiedDate: new Date().toISOString(),
      expanded: false,
    };

    // 使用安全更新方式更新主数据
    await updateMainDataSafely(async (mainData) => {
      // 确定目标位置
      let targetChildren: Array<FolderNode | SubscriptionNode | ParagraphNode>;

      if (parentId) {
        // 查找父文件夹
        const parentFolder = findFolderNode(mainData.children, parentId);
        if (!parentFolder) {
          throw new Error(`未找到父文件夹: ${parentId}`);
        }

        // 设置路径和层级
        newFolder.path =
          parentFolder.path === "/"
            ? `/${parentFolder.name}`
            : `${parentFolder.path}/${parentFolder.name}`;
        newFolder.level = parentFolder.level + 1;

        // 在父文件夹的子节点数组开头添加新文件夹
        targetChildren = parentFolder.children;
      } else {
        // 在最外层创建
        newFolder.path = "/";
        newFolder.level = 0;

        // 在主数据的子节点数组开头添加新文件夹
        targetChildren = mainData.children;
      }

      // 在数组开头添加新文件夹，确保它在最上面
      targetChildren.unshift(newFolder);

      return mainData;
    });

    console.log(`成功创建文件夹: ${newFolder.name} (${newFolder.id})`);
    return newFolder;
  } catch (error) {
    console.error("创建文件夹失败:", error);
    throw error;
  }
}

/**
 * 递归查找并更新节点名称
 * @param nodes 节点数组
 * @param id 要更新的节点ID
 * @param type 要更新的节点类型
 * @param newName 新名称
 * @returns boolean 是否找到并更新了节点
 */
function findAndUpdateNodeName(
  nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>,
  id: string,
  type: "folder" | "subscription",
  newName: string
): boolean {
  for (const node of nodes) {
    // 检查当前节点是否匹配
    if (node.id === id && node.type === type) {
      // 更新名称
      node.name = newName;
      node.lastModifiedDate = new Date().toISOString();
      return true;
    }

    // 如果是文件夹节点，递归检查子节点
    if (node.type === "folder") {
      const foundAndUpdated = findAndUpdateNodeName(
        node.children,
        id,
        type,
        newName
      );
      if (foundAndUpdated) {
        return true;
      }
    }
  }
  return false;
}

/**
 * 重命名文件夹或订阅源
 * @param id 要重命名的节点ID
 * @param type 要重命名的节点类型（"folder" 或 "subscription"）
 * @param newName 新名称
 * @returns Promise<boolean> 是否成功重命名
 */
export async function renameNode(
  id: string,
  type: "folder" | "subscription",
  newName: string
): Promise<boolean> {
  try {
    // 验证新名称
    if (!newName || !newName.trim()) {
      throw new Error("新名称不能为空");
    }

    // 使用安全更新方式更新主数据
    let nodeUpdated = false;

    await updateMainDataSafely(async (mainData) => {
      // 查找并更新节点名称
      nodeUpdated = findAndUpdateNodeName(mainData.children, id, type, newName);
      return mainData;
    });

    if (nodeUpdated) {
      console.log(
        `成功重命名${
          type === "folder" ? "文件夹" : "订阅源"
        }: ${id} -> ${newName}`
      );
      return true;
    } else {
      console.log(
        `未找到要重命名的${type === "folder" ? "文件夹" : "订阅源"}: ${id}`
      );
      return false;
    }
  } catch (error) {
    console.error(
      `重命名${type === "folder" ? "文件夹" : "订阅源"}失败:`,
      error
    );
    throw error;
  }
}
