/**
 * 工具函数统一导出文件
 * 提供所有工具函数的便捷导入方式
 */

// Vue 响应式对象处理
export {
  cleanReactiveObject,
  deepCopyWithoutReactive,
} from "./reactiveUtils";

// 图片处理
export {
  extractFirstImageFromContent,
  handleImageError,
} from "./imageUtils";

// 日期时间处理
export {
  formatDate,
  formatTimeAgo,
} from "./dateUtils";

// UI 相关
export {
  getAvatarColor,
  getTitleInitial,
} from "./uiUtils";

// 文章相关
export {
  getCustomTags,
  getArticleImage,
} from "./articleUtils";
