/* InputTag 组件样式 - 与项目整体风格保持一致 */
.input-tag-container {
  @apply relative w-full;
}

/* 主输入框容器 */
.input-tag-container .input-tag-wrapper {
  @apply flex flex-wrap items-center gap-2 px-4 py-3 rounded-lg border-2 border-solid min-h-12 transition-all duration-200;
  background-color: var(--color-bg-primary, #27282e);
  border-color: var(--color-border, #374151);
  color: var(--color-text-primary, #e5e7eb);
}

.input-tag-container .input-tag-wrapper.is-focused {
  border-color: var(--color-accent, #656fac);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.input-tag-container .input-tag-wrapper.has-dropdown {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* 已选择的标签 */
.input-tag-container .selected-tags {
  @apply flex flex-wrap gap-2;
}

.input-tag-container .tag-item {
  @apply flex items-center gap-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200;
  background-color: rgba(99, 102, 241, 0.1);
  color: #a5b4fc;
  border: 1px solid rgba(99, 102, 241, 0.3);
}

.input-tag-container .tag-item:hover {
  background-color: rgba(99, 102, 241, 0.2);
}

.input-tag-container .tag-remove {
  @apply flex items-center justify-center w-4 h-4 rounded-full transition-all duration-200 border-none bg-transparent cursor-pointer;
  color: #9ca3af;
}

.input-tag-container .tag-remove:hover {
  @apply bg-red-500 bg-opacity-20 text-red-400;
}

/* 输入框 */
.input-tag-container .tag-input {
  @apply flex-1 min-w-32 bg-transparent border-none outline-none text-sm;
  color: var(--color-text-primary, #e5e7eb);
}

.input-tag-container .tag-input::placeholder {
  color: var(--color-text-secondary, #9ca3af);
}

/* 清空按钮 */
.input-tag-container .clear-button {
  @apply flex items-center justify-center w-6 h-6 rounded-full transition-all duration-200 border-none bg-transparent cursor-pointer;
  color: #9ca3af;
}

.input-tag-container .clear-button:hover {
  @apply bg-gray-600 bg-opacity-50 text-gray-300;
}

/* 下拉菜单 */
.input-tag-container .dropdown-menu {
  @apply absolute top-full left-0 right-0 z-50 max-h-64 overflow-y-auto rounded-b-lg border-2 border-t-0 border-solid;
  background-color: var(--color-bg-primary, #27282e);
  border-color: var(--color-accent, #656fac);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 隐藏滚动条但保持滚动功能 */
.input-tag-container .dropdown-menu {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.input-tag-container .dropdown-menu::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.input-tag-container .dropdown-item {
  @apply flex items-center justify-between px-4 py-3 cursor-pointer transition-all duration-200;
  color: var(--color-text-primary, #e5e7eb);
  border-bottom: 1px solid rgba(55, 65, 81, 0.5);
}

.input-tag-container .dropdown-item:last-child {
  border-bottom: none;
}

.input-tag-container .dropdown-item:hover,
.input-tag-container .dropdown-item.is-highlighted {
  background-color: rgba(99, 102, 241, 0.1);
}

.input-tag-container .dropdown-item.is-selected {
  @apply text-blue-400;
  background-color: rgba(99, 102, 241, 0.05);
}

.input-tag-container .option-text {
  @apply flex-1 text-sm;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-tag-container .input-tag-wrapper {
    @apply px-3 py-2;
  }

  .input-tag-container .tag-item {
    @apply text-xs px-1.5 py-0.5;
  }

  .input-tag-container .tag-input {
    @apply text-sm min-w-24;
  }

  .input-tag-container .dropdown-item {
    @apply px-3 py-2;
  }

  .input-tag-container .option-text {
    @apply text-sm;
  }
}

/* 动画效果 */
.input-tag-container .dropdown-menu {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 空状态 */
.input-tag-container .dropdown-empty {
  @apply px-4 py-6 text-center;
  color: var(--color-text-secondary, #9ca3af);
}

.input-tag-container .dropdown-empty-text {
  @apply text-sm;
}

/* 加载状态 */
.input-tag-container .dropdown-loading {
  @apply flex items-center justify-center px-4 py-6;
  color: var(--color-text-secondary, #9ca3af);
}

.input-tag-container .loading-spinner {
  @apply w-4 h-4 border-2 border-gray-600 border-t-blue-500 rounded-full animate-spin mr-2;
}

/* 最大标签数量提示 */
.input-tag-container .max-tags-hint {
  @apply text-xs mt-1;
  color: var(--color-text-secondary, #9ca3af);
}

.input-tag-container .max-tags-warning {
  @apply text-yellow-400;
}

/* 错误状态 */
.input-tag-container .input-tag-wrapper.has-error {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* 禁用状态 */
.input-tag-container .input-tag-wrapper.is-disabled {
  @apply opacity-50 cursor-not-allowed;
  background-color: rgba(55, 65, 81, 0.5);
}

.input-tag-container .input-tag-wrapper.is-disabled .tag-input {
  @apply cursor-not-allowed;
}

.input-tag-container .input-tag-wrapper.is-disabled .tag-remove,
.input-tag-container .input-tag-wrapper.is-disabled .clear-button {
  @apply cursor-not-allowed opacity-50;
}

/* 紧凑模式 - 用于导航栏等空间受限的场景 */
.input-tag-container.compact {
  font-size: 0.875rem;
}

.input-tag-container.compact .input-tag-wrapper {
  min-height: 32px;
  padding: 4px 8px;
  gap: 4px;
}

.input-tag-container.compact .tag-item {
  padding: 2px 6px;
  font-size: 0.75rem;
  height: 20px;
  line-height: 16px;
}

.input-tag-container.compact .tag-remove {
  width: 14px;
  height: 14px;
  font-size: 10px;
  line-height: 12px;
}

.input-tag-container.compact .tag-input {
  font-size: 0.875rem;
  padding: 4px 0;
  min-width: 80px;
}

.input-tag-container.compact .clear-button {
  width: 20px;
  height: 20px;
  font-size: 14px;
}

.input-tag-container.compact .dropdown-menu {
  font-size: 0.875rem;
  max-height: 160px;
}

.input-tag-container.compact .dropdown-item {
  padding: 6px 8px;
  font-size: 0.875rem;
}

/* 移动端紧凑模式调整 */
@media (max-width: 768px) {
  .input-tag-container.compact .input-tag-wrapper {
    min-height: 28px;
    padding: 3px 6px;
  }

  .input-tag-container.compact .tag-item {
    padding: 1px 4px;
    font-size: 0.7rem;
    height: 18px;
  }

  .input-tag-container.compact .tag-input {
    font-size: 0.8rem;
    min-width: 60px;
  }
}
