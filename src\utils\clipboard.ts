/**
 * 剪贴板操作工具函数
 */

/**
 * 复制文本到剪贴板的通用函数
 * @param text 要复制的文本
 * @param successMessage 成功提示消息，默认为"链接已复制"
 * @param showAlert 在非uTools环境下是否显示alert，默认为true
 */
export async function copyToClipboard(
  text: string,
  successMessage: string = "链接已复制",
  showAlert: boolean = true
): Promise<void> {
  if (!text) {
    console.warn("复制内容为空");
    return;
  }

  console.log("尝试复制文本:", text);

  // 优先使用 uTools API
  if ((window as any).utoolsAPI) {
    console.log("使用 uTools API 复制");
    try {
      (window as any).utoolsAPI.copyText(text);
      (window as any).utoolsAPI.showNotification(successMessage);
      console.log("✅ uTools API 复制成功");
      return;
    } catch (error) {
      console.error("uTools API 复制失败:", error);
      // 继续尝试其他方法
    }
  }

  // 尝试现代 Clipboard API
  if (navigator.clipboard && window.isSecureContext) {
    console.log("使用现代 Clipboard API 复制");
    try {
      await navigator.clipboard.writeText(text);
      console.log("✅ 现代 Clipboard API 复制成功");
      if (showAlert) {
        if ((window as any).utoolsAPI) {
          (window as any).utoolsAPI.showNotification(successMessage);
        } else {
          alert(successMessage);
        }
      }
      return;
    } catch (clipboardError) {
      console.warn("现代 Clipboard API 失败:", clipboardError);
      // 继续尝试降级方案
    }
  }

  // execCommand 降级方案
  console.log("使用 execCommand 降级方案");
  try {
    const success = await copyWithExecCommand(text);
    if (success) {
      console.log("✅ execCommand 复制成功");
      if (showAlert) {
        if ((window as any).utoolsAPI) {
          (window as any).utoolsAPI.showNotification(successMessage);
        } else {
          alert(successMessage);
        }
      }
      return;
    }
  } catch (execError) {
    console.error("execCommand 复制失败:", execError);
  }

  // 所有方法都失败，提供手动复制选项
  console.error("所有复制方法都失败");
  if (showAlert) {
    const shouldShowText = confirm("自动复制失败，是否显示文本供您手动复制？");
    if (shouldShowText) {
      prompt("请手动复制以下文本:", text);
    }
  }
  throw new Error("复制失败");
}

/**
 * 使用 execCommand 复制文本的辅助函数
 * @param text 要复制的文本
 * @returns Promise<boolean> 是否成功
 */
function copyWithExecCommand(text: string): Promise<boolean> {
  return new Promise((resolve) => {
    let textArea: HTMLTextAreaElement | null = null;

    try {
      // 创建临时文本区域
      textArea = document.createElement("textarea");
      textArea.value = text;

      // 设置样式使其不可见但仍可选择
      textArea.style.position = "fixed";
      textArea.style.top = "0";
      textArea.style.left = "0";
      textArea.style.width = "1px";
      textArea.style.height = "1px";
      textArea.style.padding = "0";
      textArea.style.border = "none";
      textArea.style.outline = "none";
      textArea.style.boxShadow = "none";
      textArea.style.background = "transparent";
      textArea.style.opacity = "0";
      textArea.style.pointerEvents = "none";
      textArea.style.zIndex = "-1";
      textArea.setAttribute("readonly", "");

      document.body.appendChild(textArea);

      // 选择文本
      textArea.focus();
      textArea.select();
      textArea.setSelectionRange(0, text.length);

      // 执行复制命令
      const successful = (document as any).execCommand("copy");
      console.log("execCommand 返回:", successful);

      resolve(successful);
    } catch (error) {
      console.error("execCommand 执行异常:", error);
      resolve(false);
    } finally {
      // 清理
      if (textArea && textArea.parentNode) {
        document.body.removeChild(textArea);
      }
    }
  });
}

/**
 * 复制链接的便捷函数
 * @param url 要复制的链接
 */
export function copyLink(url: string): Promise<void> {
  return copyToClipboard(url, "链接已复制");
}

/**
 * 复制文章链接的便捷函数
 * @param url 文章链接
 */
export function copyArticleLink(url: string): Promise<void> {
  return copyToClipboard(url, "文章链接已复制");
}
