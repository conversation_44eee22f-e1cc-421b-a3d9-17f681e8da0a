/* 全局样式 - Obsidian 深色风格 */

/* 基础重置 */
* {
  @apply box-border;
}

html {
  @apply h-full;
}

body {
  @apply h-full m-0 font-sans text-gray-100 bg-gray-900;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  @apply h-full;
}

/* 隐藏所有滚动条但保持滚动功能 */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

*::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 确保body和html也隐藏滚动条 */
html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
}

html {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

body {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* 链接样式 - 深色主题 */
a {
  @apply text-indigo-400 no-underline;
}

a:hover {
  @apply text-indigo-300 underline;
}

/* 代码样式 - 深色主题 */
code {
  @apply bg-gray-800 text-gray-200 px-2 py-1 rounded text-sm font-mono;
}

pre {
  @apply bg-gray-800 text-gray-200 p-4 rounded-lg border border-gray-700;
  overflow: hidden;
  word-wrap: break-word;
  white-space: pre-wrap;
}

pre code {
  @apply bg-transparent p-0;
}

/* 表单元素 */
input,
textarea,
select {
  @apply font-sans;
}

button {
  @apply font-sans;
}

/* 工具类 */
.text-shadow {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.transition-base {
  @apply transition-all duration-200 ease-in-out;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

/* 标题样式 */
h1,
h2,
h3,
h4,
h5,
h6 {
  @apply text-gray-100 font-medium;
}

h1 {
  @apply text-2xl mb-6;
}

h2 {
  @apply text-xl mb-4;
}

h3 {
  @apply text-lg mb-3;
}

/* 打印样式 */
@media print {
  .no-print {
    @apply hidden;
  }
}

/* 通用折叠文件夹样式 */
.nav-directory-item {
  @apply flex justify-start items-center cursor-pointer rounded px-2 py-1 transition-colors duration-200;
}

.nav-directory-item:hover {
  background-color: rgba(0, 0, 0, 0.1);
  transform: translateX(2px);
}

.dark .nav-directory-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}
