"use strict";
/**
 * RSS 处理模块 - 使用专业的 RSS 解析库
 * 只包含两个核心函数：订阅和获取内容
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RSSHandler = void 0;
const rss_parser_1 = __importDefault(require("rss-parser"));
class RSSHandler {
    constructor() {
        // 初始化 RSS 解析器
        this.parser = new rss_parser_1.default({
            timeout: 10000, // 10秒超时
            headers: {
                "User-Agent": "uTools RSS Browser/1.0",
            },
            customFields: {
                feed: ["language", "copyright", "managingEditor"],
                item: ["media:content", "enclosure", "category"],
            },
        });
    }
    /**
     * 函数1: 订阅 RSS 源（使用 rss-parser 库）
     * @param url RSS 源地址
     * @returns Promise<RSSData> RSS 数据
     */
    async subscribeRSS(url) {
        try {
            console.log("开始订阅 RSS:", url);
            // 使用 rss-parser 直接解析 RSS - 一行代码搞定！
            const feed = await this.parser.parseURL(url);
            // 转换为我们的数据格式
            const rssData = {
                title: feed.title,
                description: feed.description,
                link: feed.link,
                feedUrl: feed.feedUrl,
                language: feed.language,
                lastBuildDate: feed.lastBuildDate,
                items: feed.items.slice(0, 20).map((item) => ({
                    title: item.title,
                    link: item.link,
                    content: item.content,
                    contentSnippet: item.contentSnippet,
                    pubDate: item.pubDate,
                    creator: item.creator,
                    sourceId: feed.feedUrl,
                    categories: item.categories,
                })),
            };
            console.log("RSS 订阅成功:", rssData.title);
            // utools.showNotification(`成功订阅: ${rssData.title || "未知RSS源"}`);
            return rssData;
        }
        catch (error) {
            console.error("RSS 订阅失败:", error);
            const errorMessage = error instanceof Error ? error.message : "RSS 订阅失败";
            utools.showNotification(`订阅失败: ${errorMessage}`);
            throw error;
        }
    }
    /**
     * 函数2: 获取并显示 RSS 内容
     * @param url RSS 源地址
     */
    async displayRSSContent(url) {
        try {
            // 订阅 RSS 获取数据
            const rssData = await this.subscribeRSS(url);
            // 将数据传递给渲染进程显示
            window.utoolsPayload = {
                type: "rss-content",
                data: rssData,
            };
            // 跳转到 RSS 显示页面
            utools.redirect("rss", { type: "text", data: rssData });
        }
        catch (error) {
            console.error("显示 RSS 内容失败:", error);
        }
    }
}
exports.RSSHandler = RSSHandler;
