<template>
  <nav class="bookmarks-navigation">
    <div class="nav-header">
      <InputTag
        v-model="searchTags"
        :options="searchOptions"
        placeholder="搜索标签..."
        compact
      />
      <!-- <div class="nav-header-actions">
        <div class="nav-header-icon-btn" title="添加书签">
          <div class="text-size-lg i-material-symbols-bookmark-add-outline" />
        </div>
        <div class="nav-header-icon-btn" title="刷新" @click="refreshBookmarks">
          <div class="text-size-lg i-material-symbols-refresh" />
        </div>
      </div> -->
    </div>

    <div class="bookmarks-list">
      <!-- 书签统计信息 -->
      <div v-if="bookmarkArticles.length > 0" class="nav-info">
        <div class="nav-info-item">
          <span class="nav-info-label">总计</span>
          <span class="nav-info-value">{{ bookmarkArticles.length }}</span>
        </div>
        <div v-if="searchTags.length > 0" class="nav-info-item">
          <span class="nav-info-label">筛选</span>
          <span class="nav-info-value">{{ filteredBookmarks.length }}</span>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredBookmarks.length === 0" class="empty-state">
        <div class="empty-icon">
          <div class="text-size-xl i-material-symbols-bookmark-outline" />
        </div>
        <div class="empty-text">
          <p v-if="bookmarkArticles.length === 0">暂无书签</p>
          <p v-else>没有找到匹配的书签</p>
        </div>
      </div>

      <!-- 书签列表 -->
      <div
        v-for="article in filteredBookmarks"
        :key="article.id"
        class="bookmark-item"
        @click="navigateToArticle(article)"
      >
        <div class="bookmark-cover">
          <img
            v-if="getArticleImage(article)"
            :src="getArticleImage(article)"
            :alt="article.title"
            class="cover-image"
            @error="handleImageError"
          />
          <div
            v-else
            class="cover-placeholder"
            :style="{ backgroundColor: getAvatarColor(article.title) }"
          >
            {{ getTitleInitial(article.title) }}
          </div>
        </div>
        <div class="bookmark-content">
          <div class="bookmark-title" :title="article.title">
            {{ article.title }}
          </div>
          <div class="bookmark-header">
            <span class="bookmark-author" :title="article.creator">
              {{ article.creator || "未知作者" }}
            </span>
            <span class="bookmark-time">
              {{ formatTimeAgo(article.pubDate) }}
            </span>
          </div>
          <div v-if="getCustomTags(article).length > 0" class="bookmark-tags">
            <span
              v-for="tag in getCustomTags(article)"
              :key="tag"
              class="bookmark-tag"
            >
              {{ tag }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { InputTag } from "@/components/InputTag";
import { ref, computed, onMounted } from "vue";
import { useRSSStore } from "@/stores/rssStore";
import { SystemTag, TagType } from "@/types/rss/rss-content";
import type { RSSContentItem } from "@/types/rss/rss-content";
import { useRouter } from "vue-router";
import {
  getAvatarColor,
  getTitleInitial,
  getArticleImage,
  handleImageError,
  formatTimeAgo,
  getCustomTags,
} from "@/services/utils";

const rssStore = useRSSStore();
const router = useRouter();

const navigateToArticle = (article: RSSContentItem) => {
  // 将文章数据存储到全局变量
  (window as any).currentArticle = article;

  // 检查当前是否已经在文章页面
  if (router.currentRoute.value.name === "RSSArticle") {
    // 如果已经在文章页面，触发一个自定义事件来通知文章组件更新
    window.dispatchEvent(
      new CustomEvent("article-changed", { detail: article })
    );
  } else {
    // 如果不在文章页面，导航到文章详情页面
    router.push({ name: "RSSArticle" });
  }
};

// 搜索标签
const searchTags = ref<string[]>([]);

// 获取所有书签文章
const bookmarkArticles = computed(() => {
  return rssStore.getArticlesByTag(TagType.SYSTEM, [SystemTag.BOOKMARK]);
});

// 根据搜索标签过滤书签
const filteredBookmarks = computed(() => {
  if (searchTags.value.length === 0) {
    return bookmarkArticles.value;
  }

  return bookmarkArticles.value.filter((article) => {
    // 检查文章是否包含搜索的标签
    return searchTags.value.some((searchTag) => {
      // 搜索标题
      if (article.title.toLowerCase().includes(searchTag.toLowerCase())) {
        return true;
      }
      // 搜索作者
      if (article.creator?.toLowerCase().includes(searchTag.toLowerCase())) {
        return true;
      }
      // 搜索自定义标签
      if (
        article.marks?.some(
          (mark) =>
            mark.startsWith("#") &&
            mark.toLowerCase().includes(searchTag.toLowerCase())
        )
      ) {
        return true;
      }
      return false;
    });
  });
});

// 获取所有自定义标签作为搜索选项
const searchOptions = computed(() => {
  const customTags = new Set<string>();

  bookmarkArticles.value.forEach((article) => {
    article.marks?.forEach((mark) => {
      if (mark.startsWith("#")) {
        customTags.add(mark.substring(1)); // 去掉 # 前缀
      }
    });
  });

  return Array.from(customTags).sort();
});

// 组件挂载时获取数据
onMounted(() => {
  if (bookmarkArticles.value.length === 0) {
    rssStore.fetchAllData();
  }
});
</script>

<style scoped src="./BookmarksNavigation.css"></style>
