export interface MainDataDocument {
  _id: "rss-browser/main-data"; // 固定 ID
  _rev?: string; // 版本号
  children: Array<FolderNode | SubscriptionNode | ParagraphNode>; // 顶层节点，可以是文件夹或订阅源
  lastModifiedDate: string; // 最后修改日期
  version: string; // 数据结构版本
}

export interface FolderNode {
  type: "folder"; // 节点类型，用于区分文件夹和订阅源
  id: string; // 文件夹唯一 ID
  name: string; // 文件夹名称
  description?: string; // 文件夹描述
  path: string; // 完整路径，如 "/技术/前端/Vue"
  level: number; // 层级深度，根节点为 0
  children: Array<FolderNode | SubscriptionNode | ParagraphNode>; // 子节点，可以是文件夹或订阅源
  createdDate: string; // 创建日期
  lastModifiedDate: string; // 最后修改日期
  expanded?: boolean; // 是否展开（UI 状态）
}

export interface SubscriptionNode {
  type: "subscription"; // 节点类型，用于区分文件夹和订阅源
  id: string; // 订阅源唯一 ID
  url: string; // RSS 源地址
  name: string; // RSS 源标题
  description?: string; // RSS 源描述
  favicon?: string; // 网站图标
  lastUpdated: string; // 最后更新时间
  lastFetchTime?: string; // 最后获取时间
  unreadCount?: number; // 未读文章数
  createdDate: string; // 创建日期
  lastModifiedDate: string; // 最后修改日期
  error?: string; // 错误信息
}

export interface ParagraphNode {
  type: "paragraph"; // 节点类型，用于标识段落
  id: string; // 段落唯一 ID
  name: string; // 段落名称/标题
  path: string; // 完整路径
  level: number; // 层级深度
  children?: Array<ParagraphNode>; // 子段落
  expanded?: boolean; // 是否展开（UI 状态）
}
