<template>
  <nav class="rss-navigation">
    <div class="nav-header">
      <!-- 侧边栏顶部功能区 -->
      <div class="nav-header-btn" @click="showAddRSS = true">
        <div class="i-material-symbols-box-add-outline mr-1 text-size-lg" />
        <div class="mb-0.5">新建订阅</div>
      </div>
      <div class="nav-header-icon-btn">
        <div
          class="text-size-lg i-material-symbols-create-new-folder-outline"
          @click="createNewFolder()"
        />
      </div>
      <div class="nav-header-icon-btn">
        <div
          class="text-size-lg i-material-symbols-unfold-more"
          @click="expandAllFolders"
        />
      </div>
      <div class="nav-header-icon-btn">
        <div
          class="text-size-lg i-material-symbols-unfold-less"
          @click="collapseAllFolders"
        />
      </div>
    </div>

    <div class="nav-links">
      <!-- 全部内容 -->
      <div class="nav-directory-item" @dblclick="switchToAllArticles">
        <div
          class="i-material-symbols-present-to-all-outline-rounded mr-2 text-coolGray transition-all duration-200"
        />

        <!-- 文件/文件夹名称 -->
        <div class="select-none">全部</div>
      </div>
      <!-- 下拉列表 -->
      <DragableDirectories
        ref="dragableDirectoriesRef"
        v-model="list"
        :folder-menu-items="folderMenuItems"
        :subscription-menu-items="subscriptionMenuItems"
        :enable-selection="true"
        :selected-item-id="selectedItemId"
        @dbl-click="handleDblClick"
        @drag-end="handleDragEnd"
        @item-select="handleItemSelect"
      />
    </div>
  </nav>
  <AddRssModel :show="showAddRSS" @close="closeAddRSSModel"> </AddRssModel>
  <ConfirmModal
    :show="showDeleteConfirm"
    title="确认删除"
    :message="deleteConfirmMessage"
    confirm-text="删除"
    cancel-text="取消"
    @confirm="handleDeleteConfirm"
    @cancel="handleDeleteCancel"
    @close="handleDeleteCancel"
  />
  <RenameModel
    :show="showRenameModal"
    :current-name="currentRenameItem.name || ''"
    :item-type="currentRenameItem.type || 'folder'"
    @close="closeRenameModal"
    @confirm="handleRenameConfirm"
  />
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";
import "./RSSNavigation.css";
import DragableDirectories from "../DragableDirectories/DragableDirectories.vue";
import AddRssModel from "@/components/Models/AddRssModel/AddRssModel.vue";
import { createFolder } from "@/services/database/mainDataService";
import {
  FolderNode,
  ParagraphNode,
  SubscriptionNode,
} from "@/types/rss/main-data";
import { useRSSStore } from "@/stores/rssStore";
import { updateMainDataSafely } from "@/services/database/mainDataService";
import { cleanReactiveObject } from "@/services/utils";
import ConfirmModal from "@/components/Models/ConfirmModal/ConfirmModal.vue";
import RenameModel from "@/components/Models/RenameModel/RenameModel.vue";
import { useRouter } from "vue-router";

// 添加对 DragableDirectories 组件的引用
const dragableDirectoriesRef = ref();

const rssStore = useRSSStore();
const router = useRouter();

const showAddRSS = ref(false);

// 添加删除确认相关的状态
const showDeleteConfirm = ref(false);
const deleteConfirmMessage = ref("");
const pendingDeleteId = ref("");
const pendingDeleteType = ref<"folder" | "subscription">("folder");

// 添加选中状态管理
const selectedItemId = ref<string | null>(null);
const showRenameModal = ref(false);
const currentRenameItem = ref<{
  id: string;
  name: string;
  type: "folder" | "subscription";
}>({
  id: "",
  name: "",
  type: "folder",
});

// 监听当前源变化，更新选中状态
watch(
  [() => rssStore.currentSourceId, () => rssStore.currentSourceType],
  ([sourceId, sourceType]) => {
    if (sourceId && sourceType) {
      selectedItemId.value = sourceId;
    } else {
      selectedItemId.value = null;
    }
  },
  { immediate: true }
);

// 处理项目选中
const handleItemSelect = (
  item: FolderNode | SubscriptionNode | ParagraphNode
) => {
  selectedItemId.value = item.id;

  if (item.type === "folder") {
    rssStore.switchToFolder(item.id);
  } else if (item.type === "subscription") {
    rssStore.switchToSubscription(item.id);
  }
};

// 将list改为计算属性，直接从store获取数据
const list = computed({
  get: () => rssStore.mainData?.children || [],
  set: (value) => {
    // 当DragableDirectories修改数据时，更新store中的数据
    if (rssStore.mainData) {
      rssStore.mainData.children = value;
    }
  },
});

// 文件夹的菜单项
const folderMenuItems = ref([
  {
    label: "新建文件夹",
    action: async (element: FolderNode | SubscriptionNode) => {
      if (element.type === "folder") {
        await createNewFolder(element.id);
      }
    },
  },
  {
    label: "重命名",
    action: async (element: FolderNode | SubscriptionNode) => {
      if (element.type === "folder") {
        showRenameModal.value = true;
        currentRenameItem.value = {
          id: element.id,
          name: element.name,
          type: "folder",
        };
      }
    },
  },
  {
    label: "删除",
    action: async (element: FolderNode | SubscriptionNode) => {
      // 显示删除确认对话框
      showDeleteConfirm.value = true;
      deleteConfirmMessage.value = `您确定要删除文件夹"${element.name}"吗？此操作会同时删除相关文章与书签，且不可撤回。`;
      pendingDeleteId.value = element.id;
      pendingDeleteType.value = element.type;
    },
  },
]);

// 处理删除确认
const handleDeleteConfirm = async () => {
  if (!dragableDirectoriesRef.value) return;

  await dragableDirectoriesRef.value.executeWithFolderStatePreserved(
    async () => {
      await rssStore.deleteNodeAndRefresh(
        pendingDeleteId.value,
        pendingDeleteType.value
      );
    }
    // 不需要确保特定文件夹展开，因为删除操作后文件夹可能不存在
  );

  // 重置状态
  showDeleteConfirm.value = false;
  pendingDeleteId.value = "";
  pendingDeleteType.value = "folder";
};

// 处理删除取消
const handleDeleteCancel = () => {
  // 重置状态
  showDeleteConfirm.value = false;
  pendingDeleteId.value = "";
  pendingDeleteType.value = "folder";
};

// 订阅源的菜单项
const subscriptionMenuItems = ref([
  {
    label: "重命名",
    action: async (element: FolderNode | SubscriptionNode) => {
      if (element.type === "subscription") {
        showRenameModal.value = true;
        currentRenameItem.value = {
          id: element.id,
          name: element.name,
          type: "subscription",
        };
      }
    },
  },
  {
    label: "删除",
    action: async (element: FolderNode | SubscriptionNode) => {
      // 显示删除确认对话框
      showDeleteConfirm.value = true;
      deleteConfirmMessage.value = `您确定要删除订阅源"${element.name}"吗？此操作会同时删除相关文章与书签，且不可撤回。`;
      pendingDeleteId.value = element.id;
      pendingDeleteType.value = element.type;
    },
  },
]);

// 关闭重命名模态框
const closeRenameModal = () => {
  showRenameModal.value = false;
  currentRenameItem.value = {
    id: "",
    name: "",
    type: "folder",
  };
};

// 处理重命名确认
const handleRenameConfirm = async (newName: string) => {
  if (!dragableDirectoriesRef.value) return;

  await dragableDirectoriesRef.value.executeWithFolderStatePreserved(
    async () => {
      await rssStore.renameNode(
        currentRenameItem.value.id,
        currentRenameItem.value.type,
        newName
      );
    }
  );

  // 重置状态
  closeRenameModal();
};

// 展开所有文件夹
const expandAllFolders = () => {
  if (!rssStore.mainData) return;

  const setExpanded = (
    nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>,
    expanded: boolean
  ) => {
    nodes.forEach((node) => {
      if (node.type === "folder") {
        node.expanded = expanded;
        if (node.children && node.children.length > 0) {
          setExpanded(node.children, expanded);
        }
      }
    });
  };

  setExpanded(rssStore.mainData.children, true);
};

// 关闭所有文件夹
const collapseAllFolders = () => {
  if (!rssStore.mainData) return;

  const setExpanded = (
    nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>,
    expanded: boolean
  ) => {
    nodes.forEach((node) => {
      if (node.type === "folder") {
        node.expanded = expanded;
        if (node.children && node.children.length > 0) {
          setExpanded(node.children, expanded);
        }
      }
    });
  };

  setExpanded(rssStore.mainData.children, false);
};

const createNewFolder = async (parentId?: string, name?: string) => {
  if (!dragableDirectoriesRef.value) return;

  await dragableDirectoriesRef.value.executeWithFolderStatePreserved(
    async () => {
      // 创建文件夹
      await createFolder(parentId, name);

      // 确保获取最新的主数据
      await rssStore.fetchAllData();

      // 确保新创建的文件夹是展开状态
      if (parentId) {
        const expandedFolders =
          dragableDirectoriesRef.value.recordExpandedFolders(
            rssStore.mainData?.children || []
          );
        if (!expandedFolders.includes(parentId)) {
          expandedFolders.push(parentId);
        }
        dragableDirectoriesRef.value.restoreExpandedFolders(
          rssStore.mainData?.children || [],
          expandedFolders,
          [parentId]
        );
      }
    },
    parentId ? [parentId] : [] // 确保父文件夹展开
  );
};

const closeAddRSSModel = async () => {
  showAddRSS.value = false;
  // 不需要手动刷新，AddRssModel中的addSubscription已经刷新了数据
};

// 修改 handleDragEnd 函数
const handleDragEnd = async () => {
  if (!dragableDirectoriesRef.value) return;

  await dragableDirectoriesRef.value.executeWithFolderStatePreserved(
    async () => {
      try {
        // 使用安全更新方式更新主数据
        await updateMainDataSafely(async (mainData) => {
          // 使用工具函数清理响应式对象
          const cleanChildren = cleanReactiveObject(
            rssStore.mainData?.children || []
          );
          mainData.children = cleanChildren;
          return mainData;
        });

        // 刷新store中的数据
        await rssStore.refreshCurrentData();

        console.log("拖拽排序已保存到数据库");
      } catch (error) {
        console.error("保存拖拽排序失败:", error);
      }
    }
  );
};

// 双击事件处理
const handleDblClick = (el: FolderNode | SubscriptionNode | ParagraphNode) => {
  if (el.type === "folder") {
    rssStore.switchToFolder(el.id);
  } else if (el.type === "subscription") {
    rssStore.switchToSubscription(el.id);
  }
  router.push("/");
};

// 初始化
onMounted(async () => {
  await rssStore.fetchAllData();
});

// 添加切换视图函数
const switchToAllArticles = async () => {
  await rssStore.switchToAllArticles();
  router.push("/");
};
</script>
