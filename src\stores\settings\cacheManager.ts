// src/stores/rss/cacheManager.ts
import { useSettingsStore } from "@/stores/settingsStore";
import {
  getArticles,
  createRSSContent,
} from "@/services/database/rssContentService";
import {
  SystemTag,
  RSSContentDocument,
  RSSContentItem,
} from "@/types/rss/rss-content";
import { ref } from "vue";

// 添加进度回调类型定义
type CacheProgressCallback = (progress: {
  current: number;
  total: number;
  currentSource?: string;
  errors?: string[];
}) => void;

/**
 * 缓存管理器
 * 提供文章缓存清理功能，根据设置的时间保留缓存，并保护书签文章不被清理
 */
export function useCacheManager() {
  const settingsStore = useSettingsStore();

  // 缓存清理进度状态
  const cacheProgress = ref(0);
  const cacheTotal = ref(0);
  const cacheCurrentSource = ref<string | null>(null);
  const cacheErrors = ref<string[]>([]);
  const isCleaningCache = ref(false);

  // 显示缓存清理模态框
  const showCacheProgressModal = () => {
    cacheProgress.value = 0;
    cacheTotal.value = 0;
    cacheCurrentSource.value = "准备清理缓存...";
    cacheErrors.value = [];
    isCleaningCache.value = true;

    // 通过全局事件触发模态框显示
    if (typeof window !== "undefined") {
      window.dispatchEvent(new Event("show-cache-modal"));
    }
  };

  // 更新缓存清理进度
  const updateCacheProgress = (progress: {
    current: number;
    total: number;
    currentSource?: string;
    errors?: string[];
  }) => {
    cacheProgress.value = progress.current;
    cacheTotal.value = progress.total;
    cacheCurrentSource.value = progress.currentSource || "正在清理缓存...";
    if (progress.errors) {
      cacheErrors.value = progress.errors;
    }
  };

  // 隐藏缓存清理模态框
  const hideCacheProgressModal = () => {
    isCleaningCache.value = false;
    setTimeout(() => {
      // 通过全局事件触发模态框隐藏
      if (typeof window !== "undefined") {
        window.dispatchEvent(new Event("hide-cache-modal"));
      }
    }, 2000);
  };

  /**
   * 将日期字符串转换为本地时间的Date对象
   * @param dateString ISO格式的日期字符串
   * @returns 本地时间的Date对象
   */
  const parseDateToLocal = (dateString: string): Date => {
    const date = new Date(dateString);

    // 如果日期字符串没有时区信息，JavaScript会将其解析为本地时间
    // 如果有时区信息（如Z或+08:00），需要转换为本地时间
    if (
      dateString.includes("Z") ||
      dateString.includes("+") ||
      dateString.includes("-")
    ) {
      // 将UTC时间转换为本地时间
      return new Date(date.getTime() + date.getTimezoneOffset() * 60000);
    }

    // 如果没有时区信息，已经是本地时间，直接返回
    return date;
  };

  /**
   * 获取当前本地日期的开始时间（00:00:00.000）
   * @returns 当前本地日期的开始时间
   */
  const getLocalDateStart = (): Date => {
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    return now;
  };

  /**
   * 清理过期的文章缓存
   * @param retentionDays 保留天数，0表示不清理
   * @param cleanupMode 缓存清理模式：clear-清空内容，delete-删除整个内容数据
   * @param progressCallback 进度回调函数
   * @returns Promise<{ success: boolean, cleanedCount: number, message: string }>
   */
  const cleanExpiredCache = async (
    retentionDays: number,
    cleanupMode: "clear" | "delete" = "clear",
    progressCallback?: CacheProgressCallback
  ): Promise<{
    success: boolean;
    cleanedCount: number;
    message: string;
  }> => {
    try {
      // 如果保留时间为0，表示不清理
      if (retentionDays <= 0) {
        return {
          success: true,
          cleanedCount: 0,
          message: "缓存清理已禁用",
        };
      }

      // 获取所有文章
      const { items: allArticles } = await getArticles();

      // 计算截止日期（本地时间）
      const cutoffDate = getLocalDateStart();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      // 按订阅源分组文章
      const articlesBySource: Record<string, RSSContentItem[]> = {};

      // 遍历所有文章，按订阅源分组并标记需要清理的文章
      for (const article of allArticles) {
        // 使用本地时间解析文章发布日期
        const articleDate = parseDateToLocal(article.pubDate);

        // 检查文章是否过期且不是书签
        if (
          articleDate < cutoffDate &&
          !article.marks.includes(SystemTag.BOOKMARK)
        ) {
          // 从文章ID中提取订阅源ID
          const sourceId = article.sourceId;

          // 按订阅源分组
          if (!articlesBySource[sourceId]) {
            articlesBySource[sourceId] = [];
          }
          articlesBySource[sourceId].push(article);
        }
      }

      // 获取需要处理的订阅源总数
      const totalSources = Object.keys(articlesBySource).length;
      let processedSources = 0;
      const errors: string[] = [];

      // 更新每个订阅源的文章内容
      let cleanedCount = 0;
      for (const [sourceId, articles] of Object.entries(articlesBySource)) {
        try {
          // 通知进度更新
          processedSources++;
          if (progressCallback) {
            progressCallback({
              current: processedSources,
              total: totalSources,
              currentSource: `正在清理订阅源: ${sourceId}`,
              errors,
            });
          }

          // 获取订阅源的完整内容
          const docId = `rss-browser/content/${sourceId}`;
          const rssContent = (await utools.db.get(
            docId
          )) as RSSContentDocument | null;

          if (rssContent && rssContent.items) {
            if (cleanupMode === "clear") {
              // clear模式：只清空内容，保留标题、时间和链接
              rssContent.items = rssContent.items.map(
                (item: RSSContentItem) => {
                  const cleanedArticle = articles.find((a) => a.id === item.id);
                  if (cleanedArticle) {
                    cleanedCount++;
                    return {
                      ...item,
                      content: undefined,
                      contentSnippet: undefined,
                    };
                  }
                  return item;
                }
              );
            } else if (cleanupMode === "delete") {
              // delete模式：删除整个文章内容信息
              const articleIdsToDelete = articles.map((a) => a.id);
              const originalCount = rssContent.items.length;
              rssContent.items = rssContent.items.filter(
                (item: RSSContentItem) => !articleIdsToDelete.includes(item.id)
              );
              cleanedCount += originalCount - rssContent.items.length;
            }

            // 保存更新后的内容
            await createRSSContent(rssContent);
          }
        } catch (error) {
          console.error(`更新订阅源 ${sourceId} 的文章内容失败:`, error);
          errors.push(
            `更新订阅源 ${sourceId} 失败: ${
              error instanceof Error ? error.message : String(error)
            }`
          );

          // 通知进度更新（包含错误）
          if (progressCallback) {
            progressCallback({
              current: processedSources,
              total: totalSources,
              currentSource: `清理订阅源失败: ${sourceId}`,
              errors,
            });
          }
        }
      }

      // 根据保留天数生成友好的时间文本
      let timeText = "";
      if (retentionDays === 1) {
        timeText = "1天";
      } else if (retentionDays < 30) {
        timeText = `${retentionDays}天`;
      } else if (retentionDays === 30) {
        timeText = "1个月";
      } else if (retentionDays === 90) {
        timeText = "3个月";
      } else if (retentionDays === 180) {
        timeText = "6个月";
      } else if (retentionDays === 365) {
        timeText = "1年";
      } else {
        timeText = `${Math.round(retentionDays / 30)}个月`;
      }

      // 根据清理模式生成不同的消息
      const modeText = cleanupMode === "clear" ? "清空内容" : "删除文章";
      return {
        success: true,
        cleanedCount,
        message: `已${modeText}超过 ${timeText} 的 ${cleanedCount} 篇过期文章`,
      };
    } catch (error) {
      console.error("清理过期缓存失败:", error);
      return {
        success: false,
        cleanedCount: 0,
        message: `清理缓存失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
    }
  };

  /**
   * 手动清理缓存
   * @param retentionDays 保留天数
   * @param cleanupMode 缓存清理模式：clear-清空内容，delete-删除整个内容数据
   * @param progressCallback 进度回调函数
   * @returns Promise<{ success: boolean, cleanedCount: number, message: string }>
   */
  const clearCache = async (
    retentionDays?: number,
    cleanupMode?: "clear" | "delete",
    progressCallback?: CacheProgressCallback
  ): Promise<{
    success: boolean;
    cleanedCount: number;
    message: string;
  }> => {
    try {
      // 如果没有提供保留天数，使用设置中的值
      const days = retentionDays ?? settingsStore.cacheRetentionTime;

      // 如果没有提供清理模式，使用设置中的值
      const mode = cleanupMode ?? settingsStore.cacheCleanupMode;

      // 执行清理
      const result = await cleanExpiredCache(days, mode, progressCallback);

      return result;
    } catch (error) {
      console.error("手动清理缓存失败:", error);
      return {
        success: false,
        cleanedCount: 0,
        message: `手动清理缓存失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
    }
  };

  /**
   * 自动清理缓存
   * 根据设置中的自动缓存清理和保留时间配置执行清理
   * @param progressCallback 进度回调函数
   * @returns Promise<{ success: boolean, cleanedCount: number, message: string }>
   */
  const autoCleanCache = async (
    progressCallback?: CacheProgressCallback
  ): Promise<{
    success: boolean;
    cleanedCount: number;
    message: string;
  }> => {
    try {
      // 如果没有启用自动清理，直接返回
      if (!settingsStore.autoCacheRetention) {
        return {
          success: true,
          cleanedCount: 0,
          message: "自动缓存清理已禁用",
        };
      }

      // 获取设置中的缓存保留时间和清理模式
      const retentionDays = settingsStore.cacheRetentionTime;
      const cleanupMode = settingsStore.cacheCleanupMode;

      // 执行清理
      const result = await cleanExpiredCache(
        retentionDays,
        cleanupMode,
        progressCallback
      );

      return result;
    } catch (error) {
      console.error("自动清理缓存失败:", error);
      return {
        success: false,
        cleanedCount: 0,
        message: `自动清理缓存失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
    }
  };

  return {
    cleanExpiredCache,
    clearCache,
    autoCleanCache,

    // 缓存清理模态框状态
    cacheProgress,
    cacheTotal,
    cacheCurrentSource,
    cacheErrors,
    isCleaningCache,
    showCacheProgressModal,
    updateCacheProgress,
    hideCacheProgressModal,
  };
}
