.loader {
  --clr: #003cff;
  --load-time: 2s;
  outline: 5px solid var(--clr);
  outline-offset: 5px;
  position: relative;
  overflow: hidden;
  border-radius: 10rem;
  width: 10rem;
  padding: 0.5rem 8rem;
}

.loader::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 300%;
  height: 300%;
  background-color: var(--clr);
  z-index: 2;
  animation: loading var(--load-time) ease-in-out infinite;
}

@keyframes loading {
  0% {
    width: 0%;
  }

  100% {
    width: 100%;
  }
}

/* 进度条样式 */
.progress-container {
  width: 100%;
  height: 10px;
  background-color: #374151;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #656fac;
  border-radius: 9999px;
  transition: width 0.3s ease-in-out;
}
