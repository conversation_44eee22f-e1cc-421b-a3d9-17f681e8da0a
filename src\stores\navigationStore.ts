// src/stores/navigationStore.ts
import { defineStore } from "pinia";
import { useRouter } from "vue-router";
import { computed, ref, watch } from "vue";

export const useNavigationStore = defineStore("navigation", () => {
  const router = useRouter();

  // 自定义历史记录栈，更可靠地跟踪导航历史
  const navigationHistory = ref<string[]>([]);
  const currentIndex = ref(-1);

  // 浏览器历史记录长度（仅用于显示）
  const historyLength = ref(1);

  // 标记是否正在进行程序化导航（前进/后退）
  const isNavigating = ref(false);

  // 初始化历史记录
  const initializeHistory = () => {
    const currentPath = router.currentRoute.value.fullPath;
    if (navigationHistory.value.length === 0) {
      navigationHistory.value = [currentPath];
      currentIndex.value = 0;
    }
  };

  // 监听路由变化，更新历史记录
  watch(
    () => router.currentRoute.value.fullPath,
    (newPath) => {
      // 确保历史记录已初始化
      if (navigationHistory.value.length === 0) {
        initializeHistory();
        return;
      }

      // 如果正在进行程序化导航，不要修改历史记录
      if (isNavigating.value) {
        console.log(
          "Programmatic navigation detected, skipping history update"
        );
        return;
      }

      // 检查是否是已存在的路径（可能是前进/后退导航）
      const existingIndex = navigationHistory.value.findIndex(
        (path) => path === newPath
      );
      if (existingIndex !== -1) {
        // 如果路径存在于历史记录中，更新当前索引
        currentIndex.value = existingIndex;
        console.log("Navigated to existing path:", {
          path: newPath,
          newIndex: existingIndex,
          history: navigationHistory.value,
        });
        return;
      }

      // 如果当前不在历史记录的末尾，删除当前位置之后的所有记录
      if (currentIndex.value < navigationHistory.value.length - 1) {
        navigationHistory.value = navigationHistory.value.slice(
          0,
          currentIndex.value + 1
        );
      }

      // 添加新路径到历史记录
      navigationHistory.value.push(newPath);
      currentIndex.value = navigationHistory.value.length - 1;

      // 更新浏览器历史长度
      historyLength.value = window.history.length;

      console.log("Navigation history updated:", {
        history: navigationHistory.value,
        currentIndex: currentIndex.value,
        currentPath: newPath,
      });
    },
    { immediate: true }
  );

  // 判断是否可以后退
  const canGoBack = computed(() => {
    return currentIndex.value > 0;
  });

  // 判断是否可以前进
  const canGoForward = computed(() => {
    return currentIndex.value < navigationHistory.value.length - 1;
  });

  // 后退方法
  const goBack = () => {
    console.log("goBack called, canGoBack:", canGoBack.value);
    if (canGoBack.value) {
      const targetIndex = currentIndex.value - 1;
      const targetPath = navigationHistory.value[targetIndex];

      console.log("Attempting to navigate back to:", {
        targetIndex,
        targetPath,
        currentPath: router.currentRoute.value.fullPath,
      });

      isNavigating.value = true;

      // 直接使用 router.push 进行导航，更可靠
      router
        .push(targetPath)
        .then(() => {
          currentIndex.value = targetIndex;
          console.log(
            "Back navigation successful, updated index to:",
            targetIndex
          );
        })
        .catch((error) => {
          console.error("Back navigation failed:", error);
        })
        .finally(() => {
          setTimeout(() => {
            isNavigating.value = false;
          }, 100);
        });
    }
  };

  // 前进方法
  const goForward = () => {
    console.log("goForward called, canGoForward:", canGoForward.value);
    console.log("Current navigation state:", {
      currentIndex: currentIndex.value,
      historyLength: navigationHistory.value.length,
      history: navigationHistory.value,
    });

    if (canGoForward.value) {
      const targetIndex = currentIndex.value + 1;
      const targetPath = navigationHistory.value[targetIndex];

      console.log("Attempting to navigate forward to:", {
        targetIndex,
        targetPath,
        currentPath: router.currentRoute.value.fullPath,
      });

      isNavigating.value = true;

      // 直接使用 router.push 进行导航，更可靠
      router
        .push(targetPath)
        .then(() => {
          currentIndex.value = targetIndex;
          console.log(
            "Forward navigation successful, updated index to:",
            targetIndex
          );
        })
        .catch((error) => {
          console.error("Forward navigation failed:", error);
        })
        .finally(() => {
          setTimeout(() => {
            isNavigating.value = false;
          }, 100);
        });
    } else {
      console.log("Cannot go forward - no forward history available");
    }
  };

  // 当前位置（用于显示，兼容原有代码）
  const currentPosition = computed(() => currentIndex.value);

  return {
    canGoBack,
    canGoForward,
    goBack,
    goForward,
    currentPosition,
    historyLength,
    // 新增：暴露历史记录用于调试
    navigationHistory: computed(() => navigationHistory.value),
    currentIndex: computed(() => currentIndex.value),
  };
});
