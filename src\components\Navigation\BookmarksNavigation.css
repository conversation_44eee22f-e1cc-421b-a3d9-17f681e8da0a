/* BookmarksNavigation 组件样式 - 与 RSSNavigation 保持一致的 Obsidian 风格 */
.bookmarks-navigation {
  @apply p-6 box-border;
  background-color: #27282e;
  border-radius: 10px;
  width: 100%;
  max-height: 100%;
  overflow-y: auto;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.bookmarks-navigation::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 导航头部 */
.bookmarks-navigation .nav-header {
  @apply flex justify-between items-center mb-3 border-b border-gray-700;
}

/* 搜索输入框样式 - 模仿 InputTag 的 tag-input 样式 */
.bookmarks-navigation .tag-input {
  @apply flex-1 min-w-32 bg-transparent border-none outline-none text-sm px-4 py-3 rounded-lg border-2 border-solid min-h-12 transition-all duration-200;
  background-color: #27282e;
  border-color: #374151;
  color: #e5e7eb;
}

.bookmarks-navigation .tag-input::placeholder {
  color: #9ca3af;
}

.bookmarks-navigation .tag-input:focus {
  border-color: #656fac;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.bookmarks-navigation .tag-input:hover:not(:focus) {
  border-color: #4b5563;
}

.bookmarks-navigation .nav-header h2 {
  @apply m-0 mb-2 text-gray-100 text-lg font-medium;
  font-weight: 500;
}

.bookmarks-navigation .subtitle {
  @apply m-0 text-gray-400 text-xs uppercase tracking-wide;
}

.nav-header-actions {
  @apply flex gap-1;
}

.bookmarks-navigation .nav-header-icon-btn {
  @apply text-gray-400 cursor-pointer rounded-lg w-8 h-8 flex items-center justify-center border-none bg-transparent;
}

.bookmarks-navigation .nav-header-icon-btn:hover {
  background-color: #33353f;
}

/* 书签列表 */
.bookmarks-navigation .bookmarks-list {
  @apply flex flex-col gap-1 mb-8;
}

/* 书签项 - 采用类似 nav-link 的样式，增加高度以适应封面 */
.bookmarks-navigation .bookmark-item {
  @apply flex items-center px-3 py-3 no-underline text-gray-300 rounded-md transition-all duration-200 cursor-pointer;
  font-size: 14px;
  font-weight: 400;
  min-height: 80px;
}

.bookmarks-navigation .bookmark-item:hover {
  @apply bg-gray-700 text-gray-100;
}

.bookmarks-navigation .bookmark-item.active {
  @apply bg-gray-700 text-white;
  background-color: rgba(99, 102, 241, 0.1);
  border-left: 3px solid #6366f1;
  padding-left: 12px;
}

/* 封面区域 */
.bookmarks-navigation .bookmark-cover {
  @apply flex-shrink-0 mr-4;
  width: 64px;
  height: 64px;
}

/* 封面图片 */
.bookmarks-navigation .cover-image {
  @apply w-full h-full rounded-lg object-cover;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

.bookmarks-navigation .bookmark-item:hover .cover-image {
  border-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.02);
}

/* 封面占位符 */
.bookmarks-navigation .cover-placeholder {
  @apply w-full h-full rounded-lg flex items-center justify-center text-white font-bold;
  font-size: 24px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

.bookmarks-navigation .bookmark-item:hover .cover-placeholder {
  border-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.02);
}

/* 内容区域 */
.bookmarks-navigation .bookmark-content {
  @apply flex-1 min-w-0;
}

/* 书签头部信息 */
.bookmarks-navigation .bookmark-header {
  @apply flex justify-between items-center mb-1;
}

.bookmarks-navigation .bookmark-author {
  @apply text-xs opacity-50;
  font-size: 10px;
}

.bookmarks-navigation .bookmark-time {
  @apply text-xs opacity-50;
  font-size: 10px;
}

/* 书签标题 */
.bookmarks-navigation .bookmark-title {
  @apply flex-1 font-normal text-sm mb-2;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 标签区域 */
.bookmarks-navigation .bookmark-tags {
  @apply flex flex-wrap gap-1 mt-2;
}

.bookmarks-navigation .bookmark-tag {
  @apply text-xs opacity-50 bg-gray-600 px-1.5 py-0.5 rounded font-mono;
  font-size: 10px;
}

.bookmarks-navigation .bookmark-item.active .bookmark-tag {
  @apply bg-indigo-600 bg-opacity-50 text-indigo-200;
}

/* 不同颜色的标签 - 保持简洁的样式 */
.bookmarks-navigation .bookmark-tag:nth-child(1) {
  @apply bg-orange-600 bg-opacity-30 text-orange-300;
}

.bookmarks-navigation .bookmark-tag:nth-child(2) {
  @apply bg-blue-600 bg-opacity-30 text-blue-300;
}

.bookmarks-navigation .bookmark-tag:nth-child(3) {
  @apply bg-green-600 bg-opacity-30 text-green-300;
}

.bookmarks-navigation .bookmark-tag:nth-child(4) {
  @apply bg-yellow-600 bg-opacity-30 text-yellow-300;
}

.bookmarks-navigation .bookmark-tag:nth-child(5) {
  @apply bg-red-600 bg-opacity-30 text-red-300;
}

/* 信息区域 - 类似 RSSNavigation 的 nav-info */
.bookmarks-navigation .bookmarks-info {
  @apply p-4 bg-gray-750 rounded-lg text-sm border border-gray-600;
  background-color: rgba(55, 65, 81, 0.5);
}

.bookmarks-navigation .bookmarks-info h3 {
  @apply text-gray-200 text-xs font-medium mb-3 uppercase tracking-wide;
}

.bookmarks-navigation .bookmarks-info .space-y-2 > div {
  @apply flex justify-between items-center py-1;
}

.bookmarks-navigation .bookmarks-info span {
  @apply text-gray-400 text-xs;
}

.bookmarks-navigation .bookmarks-info code {
  @apply bg-gray-600 text-gray-200 px-2 py-1 rounded text-xs font-mono;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bookmarks-navigation {
    @apply w-full min-h-auto;
  }

  .bookmarks-navigation .bookmarks-list {
    @apply flex-col gap-2;
  }

  .bookmarks-navigation .bookmark-item {
    @apply flex-row;
    min-height: 60px;
  }

  .bookmarks-navigation .bookmark-cover {
    width: 48px;
    height: 48px;
    @apply mr-3;
  }

  .bookmarks-navigation .bookmark-content {
    @apply flex-1;
  }

  .bookmarks-navigation .bookmark-tag {
    @apply hidden;
  }

  .bookmarks-navigation .bookmarks-info {
    @apply mt-4;
  }
}

/* 空状态样式 */
.bookmarks-navigation .empty-state {
  @apply flex flex-col items-center justify-center py-8 text-center;
}

.bookmarks-navigation .empty-icon {
  @apply text-gray-500 mb-3;
}

.bookmarks-navigation .empty-text p {
  @apply text-gray-400 text-sm m-0;
}

/* 头像占位符样式 - 保留用于向后兼容 */
.bookmarks-navigation .avatar-placeholder {
  @apply w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium;
}

/* 统计信息样式 */
.bookmarks-navigation .nav-info {
  @apply flex gap-4 mb-3 pb-2 border-b border-gray-700;
}

.bookmarks-navigation .nav-info-item {
  @apply flex items-center gap-1;
}

.bookmarks-navigation .nav-info-label {
  @apply text-gray-400 text-xs;
}

.bookmarks-navigation .nav-info-value {
  @apply text-gray-200 text-xs font-medium;
}
