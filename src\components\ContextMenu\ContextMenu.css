/* 右键菜单样式 */

/* 遮罩层 */
.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  background: transparent;
}

/* 菜单容器 */
.context-menu {
  position: fixed;
  z-index: 1000;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 160px;
  font-size: 14px;
  user-select: none;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .context-menu {
    background: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }
}

/* 菜单项 */
.context-menu .context-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

/* 菜单项悬停效果 */
.context-menu .context-menu-item:hover:not(.disabled):not(.divider) {
  background-color: #f5f5f5;
}

@media (prefers-color-scheme: dark) {
  .context-menu .context-menu-item:hover:not(.disabled):not(.divider) {
    background-color: #404040;
  }
}

/* 禁用的菜单项 */
.context-menu .context-menu-item.disabled {
  color: #999999;
  cursor: not-allowed;
}

.context-menu .context-menu-item.disabled:hover {
  background-color: transparent;
}

/* 分割线菜单项 */
.context-menu .context-menu-item.divider {
  padding: 0;
  margin: 4px 0;
  cursor: default;
}

.context-menu .context-menu-item.divider:hover {
  background-color: transparent;
}

/* 分割线 */
.context-menu .context-menu-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 0 8px;
}

@media (prefers-color-scheme: dark) {
  .context-menu .context-menu-divider {
    background-color: #404040;
  }
}

/* 菜单项图标 */
.context-menu .context-menu-icon {
  margin-right: 8px;
  font-size: 16px;
  width: 16px;
  text-align: center;
  flex-shrink: 0;
}

/* 菜单项文本 */
.context-menu .context-menu-text {
  flex: 1;
  white-space: nowrap;
}

/* 快捷键显示 */
.context-menu .context-menu-shortcut {
  margin-left: 16px;
  font-size: 12px;
  color: #999999;
  flex-shrink: 0;
}

@media (prefers-color-scheme: dark) {
  .context-menu .context-menu-shortcut {
    color: #cccccc;
  }
}

/* 菜单动画 */
.context-menu {
  animation: contextMenuFadeIn 0.15s ease-out;
  transform-origin: top left;
}

@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .context-menu {
    min-width: 140px;
    font-size: 16px;
  }

  .context-menu .context-menu-item {
    padding: 12px 16px;
  }

  .context-menu .context-menu-icon {
    font-size: 18px;
    width: 18px;
  }
}
