<template>
  <!-- 模态框遮罩层 -->
  <div
    v-if="show"
    class="modal-overlay"
    @click="closeModal"
    :style="{ zIndex: zIndex }"
  >
    <!-- 模态框内容 -->
    <div :class="`modal-content ${cardClass}`" :style="cardStyle" @click.stop>
      <div class="modal-head">
        <div class="head-title">{{ title }}</div>
        <!-- 关闭按钮 -->
        <div class="close-btn" @click="closeModal">
          <div class="i-material-symbols-close-rounded text-gray-200" />
        </div>
      </div>

      <!-- 模态框主体内容 -->
      <div class="modal-body">
        <slot>
          <h2>这是一个简单的模态框</h2>
          <p>点击 × 或者背景区域可以关闭模态框</p>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import "./SimpleModal.css";
// 定义组件的属性
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  cardClass: {
    type: String,
    default: "",
  },
  cardStyle: {
    type: String,
    default: "",
  },
  zIndex: {
    type: Number,
    default: 900, // 默认 z-index
  },
});

// 定义组件可以触发的事件
const emit = defineEmits(["close"]);

// 关闭模态框的方法
const closeModal = () => {
  emit("close");
};
</script>
