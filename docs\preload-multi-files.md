# uTools 预加载脚本多文件管理

## 🎯 设计原则

### 1. 保持主文件简单

主预加载脚本 (`services.ts`) 应该：

- 只处理基本的插件生命周期事件
- 提供最基础的 API 桥接
- 避免复杂的业务逻辑

### 2. 按功能模块分离

当需要多个功能模块时，按以下方式组织：

```
public/preload/
├── services.ts          # 主预加载脚本（入口文件）
├── modules/             # 功能模块目录
│   ├── file-handler.ts  # 文件操作模块
│   ├── rss-parser.ts    # RSS 解析模块
│   ├── database.ts      # 数据库操作模块
│   └── utils.ts         # 工具函数模块
├── types/               # 类型定义
│   └── index.ts         # 共享类型定义
├── package.json         # 预加载脚本配置
└── tsconfig.json        # TypeScript 配置
```

## 🔧 实现方案

### 方案一：单文件编译（推荐）

**优点**：

- 简单直接，只需要一个输出文件
- 符合 uTools 的预加载脚本要求
- 构建流程简单

**实现方式**：

#### 1. 主文件 (`services.ts`)

```typescript
/**
 * 主预加载脚本 - 保持简单
 */

// 导入功能模块
import { FileHandler } from "./modules/file-handler";
import { RSSParser } from "./modules/rss-parser";
import { DatabaseManager } from "./modules/database";

// 初始化模块
const fileHandler = new FileHandler();
const rssParser = new RSSParser();
const dbManager = new DatabaseManager();

// 插件生命周期
utools.onPluginEnter(({ code, type, payload }) => {
  console.log("插件进入:", { code, type, payload });

  // 根据功能代码分发到不同模块
  switch (code) {
    case "read":
      fileHandler.handleRead(payload);
      break;
    case "rss":
      rssParser.handleRSS(payload);
      break;
    default:
      // 基本处理
      utools.setExpendHeight(600);
  }
});

// 暴露统一的 API
(window as any).utoolsAPI = {
  fileHandler,
  rssParser,
  dbManager,
  // 基础 API
  showNotification: (msg: string) => utools.showNotification(msg),
  hideWindow: () => utools.hideMainWindow(),
};
```

#### 2. 功能模块示例 (`modules/file-handler.ts`)

```typescript
import * as fs from "fs";
import * as path from "path";

export class FileHandler {
  handleRead(payload: any) {
    if (!payload || payload.length === 0) {
      utools.showNotification("请选择文件");
      return;
    }

    const filePath = payload[0].path;
    try {
      const content = fs.readFileSync(filePath, "utf8");
      // 传递给渲染进程
      (window as any).utoolsPayload = {
        type: "file-read",
        data: { path: filePath, content },
      };
    } catch (error) {
      utools.showNotification("文件读取失败");
    }
  }

  async saveFile(content: string, filename: string): Promise<boolean> {
    try {
      const savePath = utools.showSaveDialog({
        title: "保存文件",
        defaultPath: filename,
      });

      if (savePath) {
        fs.writeFileSync(savePath, content, "utf8");
        return true;
      }
      return false;
    } catch {
      return false;
    }
  }
}
```

#### 3. TypeScript 配置更新

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "moduleResolution": "node",
    "outDir": "../preload",
    "rootDir": ".",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "types": ["node", "utools-api-types"]
  },
  "include": ["**/*.ts"],
  "exclude": ["node_modules", "../preload"]
}
```

### 方案二：多文件分别编译

**适用场景**：

- 功能模块完全独立
- 需要按需加载模块
- 模块间耦合度很低

**实现方式**：

#### 1. 修改 `plugin.json`

```json
{
  "preload": [
    "preload/services.js",
    "preload/file-handler.js",
    "preload/rss-parser.js"
  ]
}
```

#### 2. 独立的模块文件

每个模块都是独立的预加载脚本，有自己的生命周期处理。

## 📋 构建配置

### 更新构建脚本

#### `package.json` (主项目)

```json
{
  "scripts": {
    "build:preload": "cd public/preload && pnpm run build",
    "build:preload:watch": "cd public/preload && pnpm run dev"
  }
}
```

#### `package.json` (预加载目录)

```json
{
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "clean": "rimraf ../preload/*.js"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "rimraf": "^5.0.0",
    "@types/node": "^20.0.0",
    "utools-api-types": "^3.0.0"
  }
}
```

## 🚀 最佳实践

### 1. 保持主文件简洁

```typescript
// ✅ 好的做法
utools.onPluginEnter(({ code, payload }) => {
  moduleManager.handle(code, payload);
});

// ❌ 避免的做法
utools.onPluginEnter(({ code, payload }) => {
  // 100+ 行的复杂逻辑
});
```

### 2. 错误处理集中化

```typescript
class ErrorHandler {
  static handle(error: Error, context: string) {
    console.error(`[${context}]`, error);
    utools.showNotification(`操作失败: ${error.message}`);
  }
}
```

### 3. 类型安全

```typescript
// types/index.ts
export interface PluginPayload {
  code: string;
  type: string;
  payload: any;
}

export interface FileInfo {
  name: string;
  path: string;
  size: number;
}
```

### 4. 模块间通信

```typescript
// 使用事件系统或简单的回调
class ModuleManager {
  private modules = new Map();

  register(name: string, module: any) {
    this.modules.set(name, module);
  }

  handle(code: string, payload: any) {
    const module = this.modules.get(code);
    if (module) {
      module.handle(payload);
    }
  }
}
```

## 📝 总结

**推荐方案**：

1. **小型项目**：单文件 `services.ts`，保持简单
2. **中型项目**：单文件编译，多模块导入
3. **大型项目**：考虑多文件分别编译

**核心原则**：

- 预加载脚本越简单越好
- 复杂逻辑尽量放在渲染进程（Vue 应用）中处理
- 预加载脚本主要负责系统级 API 的桥接
