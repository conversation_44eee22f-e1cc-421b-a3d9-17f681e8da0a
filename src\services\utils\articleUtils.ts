/**
 * 文章相关工具函数
 * 用于文章数据处理和标签管理
 */

import { RSSContentItem } from "@/types/rss/rss-content";
import { extractFirstImageFromContent } from "./imageUtils";

/**
 * 获取文章的自定义标签
 * @param article RSS文章对象
 * @returns 自定义标签数组
 */
export const getCustomTags = (article: RSSContentItem): string[] => {
  return (
    article.marks
      ?.filter((mark) => mark.startsWith("#"))
      .map((mark) => mark.substring(1)) || []
  );
};

/**
 * 获取文章封面图片
 * @param article RSS文章对象
 * @returns 图片URL或空字符串
 */
export const getArticleImage = (article: RSSContentItem): string => {
  return extractFirstImageFromContent(article.content);
};
