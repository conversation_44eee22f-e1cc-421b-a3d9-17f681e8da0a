.home {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.home-header {
  margin-bottom: 24px;
  text-align: center;
}

.home-header h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 600;
}

.home-header p {
  margin: 0;
  font-size: 1rem;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 统计信息 */
.stats {
  display: flex;
  justify-content: center;
  gap: 32px;
  padding: 20px;
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.7;
}

/* 视图指示器 */
.view-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px 20px;
  margin-bottom: 24px;
  font-size: 0.875rem;
}

.view-label {
  opacity: 0.7;
}

.view-name {
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.refresh-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: transparent;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

/* 文章容器 */
.articles-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 文章项 - 改为横向布局，封面在右边 */
.rss-item {
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  align-items: stretch; /* 改为 stretch 让封面能够拉伸到内容高度 */
  gap: 20px;
  min-height: 180px; /* 设置最小高度确保封面有足够空间 */
}

.rss-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 文章内容区域 */
.article-content {
  flex: 1;
  min-width: 0;
}

/* 文章封面区域 */
.article-cover {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  width: 180px;
  min-height: 180px; /* 最小高度 */
  height: 100%; /* 自适应父容器高度 */
}

/* 封面图片 */
.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.rss-item:hover .cover-image {
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* 封面占位符 */
.cover-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 48px;
  font-weight: bold;
  border: 3px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.rss-item:hover .cover-placeholder {
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.item-title {
  margin: 0 0 12px 0;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
}

.item-link {
  text-decoration: none;
  color: inherit;
  transition: color 0.2s ease;
}

.item-link:hover {
  color: #007bff;
}

.item-description {
  margin: 0 0 16px 0;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  font-size: 0.875rem;
}

.item-date,
.source-name {
  opacity: 0.7;
}

.item-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.action-btn,
.copy-link-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: transparent;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 操作按钮图标样式 */
.action-btn > div[class*="i-material-symbols"],
.copy-link-btn > div[class*="i-material-symbols"] {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.action-btn:hover,
.copy-link-btn:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 空状态图标样式 */
.empty-icon > div[class*="i-material-symbols"] {
  width: 64px;
  height: 64px;
  color: #9ca3af;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
}

.empty-state p {
  margin: 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home {
    padding: 16px;
  }

  .stats {
    gap: 24px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .view-indicator {
    flex-direction: column;
    gap: 8px;
  }

  .view-name {
    width: 100%;
  }

  /* 移动端文章项布局调整 */
  .rss-item {
    flex-direction: column;
    gap: 16px;
  }

  .article-cover {
    width: 100%;
    height: 240px;
    order: -1; /* 在移动端将封面放到顶部 */
  }

  .item-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .item-actions {
    margin-left: 0;
  }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .action-btn,
  .copy-link-btn,
  .refresh-btn {
    border-color: #555;
  }

  .action-btn:hover,
  .copy-link-btn:hover,
  .refresh-btn:hover {
    background: #2d2d2d;
    border-color: #007bff;
  }

  .loading-spinner {
    border-color: #555;
    border-top-color: #007bff;
  }
}
