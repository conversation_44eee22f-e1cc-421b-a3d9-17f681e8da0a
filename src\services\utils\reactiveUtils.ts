/**
 * Vue 响应式对象处理工具函数
 * 用于清理和转换 Vue 响应式对象
 */

/**
 * 递归清理 Vue 响应式对象
 * @param obj 需要清理的对象
 * @returns 清理后的纯 JavaScript 对象
 */
export const cleanReactiveObject = (obj: any): any => {
  /**
   * 清理 Vue 响应式对象的工具函数
   * 将 Vue 响应式对象转换为纯 JavaScript 对象，以便可以安全地序列化和保存
   */

  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  // 如果是数组，递归处理每个元素
  if (Array.isArray(obj)) {
    return obj.map((item) => cleanReactiveObject(item));
  }

  // 如果是对象，创建一个新对象并复制所有可枚举属性
  const result: any = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      result[key] = cleanReactiveObject(obj[key]);
    }
  }

  return result;
};

/**
 * 深拷贝对象并清理 Vue 响应式属性
 * @param obj 需要拷贝的对象
 * @returns 拷贝后的纯 JavaScript 对象
 */
export const deepCopyWithoutReactive = <T>(obj: T): T => {
  return cleanReactiveObject(obj);
};
