import { createRSSContent } from "@/services/database/rssContentService";
import { MainDataDocument } from "@/types/rss/main-data";
import {
  RSSContentItem,
  SystemTag,
  TAG_PREFIXES,
  TagType,
} from "@/types/rss/rss-content";
import { Ref } from "vue";

export function useTagManagerStore(
  mainData: Ref<MainDataDocument | null>,
  articles: Ref<RSSContentItem[]>
) {
  /**
   * 更新数据库中文章的标签
   * @param articleId 文章ID
   * @param marks 更新后的标签数组
   */
  const updateArticleTagsInDatabase = async (
    articleId: string,
    marks: string[]
  ): Promise<void> => {
    // 查找包含该文章的RSS源
    let sourceId: string | null = null;

    // 遍历所有RSS源查找包含该文章的源
    if (mainData.value) {
      const findSourceContainingArticle = (nodes: any[]): boolean => {
        for (const node of nodes) {
          if (node.type === "subscription") {
            // 检查这个订阅源是否包含目标文章
            const docId = `rss-browser/content/${node.id}`;
            try {
              const rssContent = utools.db.get(docId) as any;
              if (rssContent && rssContent.items) {
                const articleInSource = rssContent.items.find(
                  (item: any) => item.id === articleId
                );
                if (articleInSource) {
                  sourceId = node.id;
                  return true;
                }
              }
            } catch (error) {
              // 继续查找其他源
            }
          } else if (node.type === "folder") {
            if (findSourceContainingArticle(node.children)) {
              return true;
            }
          }
        }
        return false;
      };

      findSourceContainingArticle(mainData.value.children);
    }

    if (sourceId) {
      // 获取RSS内容文档
      const docId = `rss-browser/content/${sourceId}`;
      const rssContent = (await utools.db.get(docId)) as any;

      if (rssContent && rssContent.items) {
        // 更新对应文章的marks字段
        const itemIndex = rssContent.items.findIndex(
          (item: any) => item.id === articleId
        );
        if (itemIndex !== -1) {
          // 更新标签
          rssContent.items[itemIndex].marks = [...marks];

          // 保存更新后的文档
          await createRSSContent(rssContent);
        }
      }
    }
  };

  /**
   * 检查文章是否包含指定的标签
   * @param articleId 文章ID
   * @param tagType 标签类型（系统标签或自定义标签）
   * @param tagValue 标签值（系统标签使用SystemTag枚举，自定义标签使用字符串）
   * @returns boolean 是否包含该标签
   */
  const hasTag = (
    articleId: string,
    tagType: TagType,
    tagValue: string | SystemTag
  ): boolean => {
    // 输入验证
    if (!articleId || typeof articleId !== "string") {
      console.error("无效的文章ID:", articleId);
      return false;
    }

    if (!tagType || !Object.values(TagType).includes(tagType)) {
      console.error("无效的标签类型:", tagType);
      return false;
    }

    if (!tagValue) {
      console.error("标签值不能为空");
      return false;
    }

    try {
      // 规范化标签值
      let normalizedTagValue: string;

      if (tagType === TagType.SYSTEM) {
        // 系统标签，直接使用枚举值
        if (!Object.values(SystemTag).includes(tagValue as SystemTag)) {
          console.error("无效的系统标签:", tagValue);
          return false;
        }
        normalizedTagValue = tagValue as string;
      } else {
        // 自定义标签，确保是字符串类型并添加#前缀
        if (typeof tagValue !== "string") {
          console.error("自定义标签值必须是字符串");
          return false;
        }
        normalizedTagValue = tagValue.trim().startsWith("#")
          ? tagValue.trim()
          : `#${tagValue.trim()}`;
      }

      // 在当前文章列表中查找目标文章
      const article = articles.value.find(
        (article) => article.id === articleId
      );

      if (!article) {
        console.warn(`未找到ID为 ${articleId} 的文章`);
        return false;
      }

      // 检查文章是否包含该标签
      return (
        Array.isArray(article.marks) &&
        article.marks.includes(normalizedTagValue)
      );
    } catch (error) {
      console.error("检查标签时发生错误:", error);
      return false;
    }
  };

  /**
   * 获取文章的所有指定类型标签
   * @param articleId 文章ID
   * @param tagType 标签类型（系统标签或自定义标签）
   * @returns string[] 文章包含的所有指定类型标签数组
   */
  const getTags = (articleId: string, tagType: TagType): string[] => {
    // 输入验证
    if (!articleId || typeof articleId !== "string") {
      console.error("无效的文章ID:", articleId);
      return [];
    }

    if (!tagType || !Object.values(TagType).includes(tagType)) {
      console.error("无效的标签类型:", tagType);
      return [];
    }

    try {
      // 在当前文章列表中查找目标文章
      const article = articles.value.find(
        (article) => article.id === articleId
      );

      if (!article || !Array.isArray(article.marks)) {
        return [];
      }

      // 根据标签类型筛选标签
      if (tagType === TagType.SYSTEM) {
        // 筛选出所有系统标签
        return article.marks.filter((mark) =>
          Object.values(SystemTag).includes(mark as SystemTag)
        );
      } else {
        // 筛选出所有自定义标签
        return article.marks.filter(
          (mark) =>
            mark.startsWith("#") &&
            !Object.values(SystemTag).includes(mark as SystemTag)
        );
      }
    } catch (error) {
      console.error("获取标签时发生错误:", error);
      return [];
    }
  };

  /**
   * 切换文章的标签
   * @param articleId 文章ID
   * @param tagType 标签类型（系统标签或自定义标签）
   * @param tagValue 标签值（系统标签使用SystemTag枚举，自定义标签使用字符串）
   * @returns Promise<boolean> 操作后是否包含该标签（true=已添加，false=已删除）
   */
  const toggleTag = async (
    articleId: string,
    tagType: TagType,
    tagValue: string | SystemTag
  ): Promise<boolean> => {
    try {
      // 输入验证
      if (!articleId || typeof articleId !== "string") {
        console.error("无效的文章ID:", articleId);
        return false;
      }

      if (!tagType || !Object.values(TagType).includes(tagType)) {
        console.error("无效的标签类型:", tagType);
        return false;
      }

      if (!tagValue) {
        console.error("标签值不能为空");
        return false;
      }

      // 规范化标签值
      let normalizedTagValue: string;

      if (tagType === TagType.SYSTEM) {
        // 系统标签，直接使用枚举值
        if (!Object.values(SystemTag).includes(tagValue as SystemTag)) {
          console.error("无效的系统标签:", tagValue);
          return false;
        }
        normalizedTagValue = tagValue as string;
      } else {
        // 自定义标签，确保是字符串类型并添加#前缀
        if (typeof tagValue !== "string") {
          console.error("自定义标签值必须是字符串");
          return false;
        }

        let customTag = tagValue.trim();
        if (!customTag) {
          console.error("自定义标签不能为空");
          return false;
        }

        normalizedTagValue = customTag.startsWith("#")
          ? customTag
          : `#${customTag}`;
      }

      // 在当前文章列表中查找目标文章
      const articleIndex = articles.value.findIndex(
        (article) => article.id === articleId
      );

      if (articleIndex === -1) {
        console.error(`未找到ID为 ${articleId} 的文章`);
        return false;
      }

      const article = articles.value[articleIndex];

      // 初始化marks数组（如果不存在）
      if (!article.marks) {
        article.marks = [];
      }

      // 检查文章是否已有该标签
      const tagIndex = article.marks.indexOf(normalizedTagValue);
      let hasTag = tagIndex !== -1;

      if (hasTag) {
        // 如果已有标签，则删除
        article.marks.splice(tagIndex, 1);
        hasTag = false;
      } else {
        // 如果没有标签，则添加
        article.marks.push(normalizedTagValue);
        hasTag = true;
      }

      // 更新store中的文章
      articles.value[articleIndex] = { ...article };

      // 更新数据库中的文章数据
      await updateArticleTagsInDatabase(articleId, article.marks);

      return hasTag;
    } catch (error) {
      console.error("切换标签失败:", error);
      return false;
    }
  };

  /**
   * 查询所有包含指定标签的文章
   * @param tagType 标签类型（系统标签或自定义标签）
   * @param tagValues 标签值数组（系统标签使用SystemTag枚举数组，自定义标签使用字符串数组）
   * @param matchAll 是否要求文章包含所有指定的标签（默认为true，即AND关系；false为OR关系）
   * @returns RSSContentItem[] 包含指定标签的所有文章
   */
  const getArticlesByTag = (
    tagType: TagType,
    tagValues: string[] | SystemTag[],
    matchAll: boolean = true
  ): RSSContentItem[] => {
    // 输入验证
    if (!tagType || !Object.values(TagType).includes(tagType)) {
      console.error("无效的标签类型:", tagType);
      return [];
    }

    if (!tagValues || !Array.isArray(tagValues) || tagValues.length === 0) {
      console.error("标签值数组不能为空");
      return [];
    }

    try {
      // 规范化标签值数组
      const normalizedTagValues: string[] = [];

      for (const tagValue of tagValues) {
        if (tagType === TagType.SYSTEM) {
          // 系统标签，直接使用枚举值
          if (!Object.values(SystemTag).includes(tagValue as SystemTag)) {
            console.error("无效的系统标签:", tagValue);
            continue;
          }
          normalizedTagValues.push(tagValue as string);
        } else {
          // 自定义标签，确保是字符串类型并添加#前缀
          if (typeof tagValue !== "string") {
            console.error("自定义标签值必须是字符串");
            continue;
          }
          const normalizedTag = tagValue.trim().startsWith("#")
            ? tagValue.trim()
            : `#${tagValue.trim()}`;
          normalizedTagValues.push(normalizedTag);
        }
      }

      // 如果没有有效的标签值，返回空数组
      if (normalizedTagValues.length === 0) {
        return [];
      }

      // 筛选出包含指定标签的文章
      return articles.value.filter((article) => {
        if (!Array.isArray(article.marks)) {
          return false;
        }

        // 检查文章是否包含指定的标签
        if (matchAll) {
          // AND关系：文章必须包含所有指定的标签
          return normalizedTagValues.every((tag) =>
            article.marks.includes(tag)
          );
        } else {
          // OR关系：文章只需包含任意一个指定的标签
          return normalizedTagValues.some((tag) => article.marks.includes(tag));
        }
      });
    } catch (error) {
      console.error("查询标签文章时发生错误:", error);
      return [];
    }
  };

  /**
   * 获取所有文章的所有标签
   * @param tagType 标签类型（可选，使用TagType枚举，不传则返回所有标签）
   * @returns string[] 去重后的标签列表
   */
  const getAllTags = (tagType?: TagType): string[] => {
    try {
      // 创建一个Set用于存储去重后的标签
      const allTags = new Set<string>();

      // 遍历所有文章
      for (const article of articles.value) {
        // 确保文章有marks数组
        if (!Array.isArray(article.marks)) {
          continue;
        }

        // 根据标签类型筛选
        for (const mark of article.marks) {
          // 判断标签类型
          const isSystemTag = Object.values(SystemTag).includes(
            mark as SystemTag
          );
          const isCustomTag = mark.startsWith(TAG_PREFIXES[TagType.CUSTOM]);

          // 如果没有指定tagType，或者标签类型匹配，则添加到结果中
          if (!tagType) {
            // 返回所有标签
            allTags.add(mark);
          } else if (tagType === TagType.SYSTEM && isSystemTag) {
            // 只返回系统标签
            allTags.add(mark);
          } else if (tagType === TagType.CUSTOM && isCustomTag) {
            // 只返回自定义标签
            allTags.add(mark);
          }
        }
      }

      // 将Set转换为数组并返回
      return Array.from(allTags);
    } catch (error) {
      console.error("获取所有标签时发生错误:", error);
      return [];
    }
  };

  return {
    // 通用标签功能
    hasTag, // 通用标签检查
    getTags, // 获取指定类型的所有标签
    toggleTag, // 切换标签
    getArticlesByTag, // 查询包含指定标签的所有文章
    getAllTags,
  };
}
