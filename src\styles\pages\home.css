/* Home 页面样式 - Obsidian 深色风格 */
.home {
  @apply p-8 max-w-4xl mx-auto;
}

.home h1 {
  @apply text-3xl font-medium mb-8 text-gray-100;
  font-weight: 500;
}

.home h2 {
  @apply text-xl font-medium mb-6 text-gray-200;
  font-weight: 500;
}

.home h3 {
  @apply text-lg font-medium mb-4 text-gray-300;
  font-weight: 500;
}

.home p {
  @apply text-gray-300 mb-4 leading-relaxed;
}

.home pre {
  @apply bg-gray-800 text-gray-200 p-4 rounded-lg text-sm border border-gray-700;
  font-family: "JetBrains Mono", "Fira Code", Consolas, monospace;
  overflow: hidden;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.welcome-info,
.features,
.route-info {
  @apply mb-6 p-6 bg-gray-800 rounded-lg border border-gray-700;
  background-color: rgba(55, 65, 81, 0.5);
}

.features ul {
  @apply list-none p-0 space-y-3;
}

.features li {
  @apply flex items-start text-gray-300;
  font-size: 14px;
  line-height: 1.6;
}

.features li strong {
  @apply font-medium text-gray-200 ml-2;
}

.route-info p {
  @apply text-gray-300 mb-2;
}

.route-info p:last-child {
  @apply mb-0;
}

.route-info code {
  @apply bg-gray-700 text-gray-200 px-2 py-1 rounded text-sm font-mono;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home {
    @apply p-4;
  }

  .welcome-info,
  .features,
  .route-info {
    @apply p-4;
  }
}
