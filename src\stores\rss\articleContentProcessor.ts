import { ParagraphNode } from "@/types/rss/main-data";
import { ref } from "vue";

export function useArticleContentProcessor() {
  const currentArticleParagraphs = ref<ParagraphNode[]>([]);
  const paragraphIdMap = ref<Map<string, string>>(new Map());

  // 设置当前文章的段落
  const setCurrentArticleParagraphs = (articleContent: string) => {
    const result = parseArticleParagraphs(articleContent);
    currentArticleParagraphs.value = result.paragraphs;
    paragraphIdMap.value = result.idMap;
  };

  // 清除当前文章的段落
  const clearCurrentArticleParagraphs = () => {
    currentArticleParagraphs.value = [];
  };

  // 在 rssStore.ts 中修改 parseArticleParagraphs 函数
  const parseArticleParagraphs = (
    articleContent: string
  ): { paragraphs: ParagraphNode[]; idMap: Map<string, string> } => {
    const paragraphs: ParagraphNode[] = [];
    const idMap = new Map<string, string>(); // 存储标题文本到ID的映射

    // 创建一个临时的DOM元素来解析HTML
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = articleContent;

    // 查找所有的标题标签 (h1, h2, h3, h4, h5, h6)
    const headers = tempDiv.querySelectorAll("h1, h2, h3, h4, h5, h6");

    // 处理每个标题
    headers.forEach((header, index) => {
      const level = parseInt(header.tagName.substring(1)); // 获取标题级别 (1-6)
      const text = header.textContent || `标题 ${index + 1}`;

      // 生成唯一ID - 使用索引和文本的哈希值
      const id = `para-${index}-${hashCode(text)}`;

      // 创建路径
      const path = `/${text}`;

      // 创建段落节点
      const paragraphNode: ParagraphNode = {
        type: "paragraph",
        id,
        name: text,
        path,
        level,
        expanded: true, // 默认展开一级标题
      };

      paragraphs.push(paragraphNode);

      // 存储标题文本到ID的映射
      idMap.set(text, id);
    });

    // 构建层级关系
    const hierarchicalParagraphs = buildParagraphHierarchy(paragraphs);

    return { paragraphs: hierarchicalParagraphs, idMap };
  };

  // 构建段落层级关系
  const buildParagraphHierarchy = (
    paragraphs: ParagraphNode[]
  ): ParagraphNode[] => {
    const root: ParagraphNode[] = [];
    const stack: ParagraphNode[] = [];

    paragraphs.forEach((paragraph) => {
      // 弹出栈中级别大于等于当前段落的元素
      while (
        stack.length > 0 &&
        stack[stack.length - 1].level >= paragraph.level
      ) {
        stack.pop();
      }

      if (stack.length === 0) {
        // 如果栈为空，添加到根级别
        root.push(paragraph);
      } else {
        // 否则，添加到栈顶元素的子元素中
        const parent = stack[stack.length - 1];
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(paragraph);
      }

      // 将当前段落压入栈中
      stack.push(paragraph);
    });

    return root;
  };

  // 添加一个简单的哈希函数
  const hashCode = (str: string): string => {
    let hash = 0;
    if (str.length === 0) return hash.toString();

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash).toString();
  };

  return {
    // 段落处理
    currentArticleParagraphs,
    paragraphIdMap,
    setCurrentArticleParagraphs,
    clearCurrentArticleParagraphs,
    parseArticleParagraphs,
    hashCode,
  };
}
