# RSS 功能使用示例

## 🎯 功能概述

我们创建了一个最简单的 RSS 订阅和显示功能，使用专业的 `rss-parser` 库，包含两个核心函数：

1. **`subscribeRSS(url)`** - 订阅 RSS 源（使用 rss-parser）
2. **`displayRSSContent(url)`** - 显示 RSS 内容

## 📁 文件结构

```
public/preload/modules/
└── rss-handler.ts          # RSS 处理模块

src/views/
└── RSS.vue                 # RSS 显示页面

src/styles/pages/
└── rss.css                 # RSS 页面样式
```

## 🔧 核心代码

### 1. RSS 处理模块 (`rss-handler.ts`)

```typescript
import Parser from "rss-parser";

export class RSSHandler {
  private parser: Parser;

  constructor() {
    // 初始化专业的 RSS 解析器
    this.parser = new Parser({
      timeout: 10000,
      headers: { "User-Agent": "uTools RSS Browser/1.0" },
      customFields: {
        feed: ["language", "copyright", "managingEditor"],
        item: ["media:content", "enclosure", "category"],
      },
    });
  }

  // 函数1: 订阅 RSS 源（使用 rss-parser - 一行代码搞定！）
  async subscribeRSS(url: string): Promise<RSSData> {
    const feed = await this.parser.parseURL(url);
    return this.convertToRSSData(feed);
  }

  // 函数2: 显示 RSS 内容
  async displayRSSContent(url: string): Promise<void> {
    const rssData = await this.subscribeRSS(url);
    (window as any).utoolsPayload = { type: "rss-content", data: rssData };
    utools.redirect("rss", { type: "text", data: rssData });
  }
}
```

### 2. Vue 页面组件 (`RSS.vue`)

```vue
<template>
  <div class="rss-page">
    <!-- RSS 订阅输入 -->
    <div class="rss-input-section">
      <input v-model="rssUrl" placeholder="输入 RSS 源地址" />
      <button @click="subscribeRSS">订阅</button>
    </div>

    <!-- RSS 内容显示 -->
    <div v-if="rssData" class="rss-content">
      <h2>{{ rssData.title }}</h2>
      <div v-for="item in rssData.items" class="rss-item">
        <h3>{{ item.title }}</h3>
        <p>{{ item.description }}</p>
      </div>
    </div>
  </div>
</template>
```

## 🚀 使用方法

### 在 uTools 中使用

1. **通过关键词触发**：

   - 输入 "rss"、"RSS"、"订阅"、"新闻"、"博客"
   - 或者直接输入 RSS 源地址

2. **通过拖拽触发**：
   - 选中 RSS 源地址，然后拖拽到 uTools

### 在浏览器中测试

1. 启动开发服务器：`pnpm run dev`
2. 访问：http://localhost:5174/#/rss
3. 输入示例 RSS 源地址进行测试

## 📋 示例 RSS 源

```
BBC News: http://feeds.bbci.co.uk/news/rss.xml
TechCrunch: https://techcrunch.com/feed/
GitHub Blog: https://github.blog/feed/
```

## 🎨 功能特点

### 1. 简单易用

- 只有两个核心函数
- 清晰的代码结构
- 易于理解和扩展

### 2. 错误处理

```typescript
try {
  const rssData = await this.subscribeRSS(url);
  // 处理成功
} catch (error) {
  utools.showNotification("RSS 订阅失败");
  // 处理错误
}
```

### 3. 用户友好

- 加载状态显示
- 错误信息提示
- 示例 RSS 源
- 响应式设计

### 4. 系统集成

- 使用 uTools 通知系统
- 支持外部链接打开
- 支持剪贴板操作

## 🔍 技术实现

### 1. RSS XML 解析

使用专业的 `rss-parser` 库解析 RSS：

```typescript
// 一行代码完成解析！
const feed = await this.parser.parseURL(url);

// 支持 RSS 1.0、RSS 2.0、Atom 等多种格式
// 自动处理 CDATA、命名空间、特殊字符等复杂情况
// 内置错误处理和超时机制
```

### 2. 专业库的优势

相比手写正则表达式解析：

```typescript
// ❌ 之前的实现 - 容易出错
private parseRSSXML(xmlContent: string): RSSData {
  const titleMatch = xmlContent.match(/<title><!\[CDATA\[(.*?)\]\]><\/title>|<title>(.*?)<\/title>/);
  // 无法处理复杂的 XML 结构、命名空间、特殊字符等
}

// ✅ 现在的实现 - 专业可靠
const feed = await this.parser.parseURL(url);
// 自动处理所有复杂情况，支持多种格式
```

### 3. 数据传递

通过全局变量在预加载脚本和渲染进程间传递数据：

```typescript
// 预加载脚本中
(window as any).utoolsPayload = { type: "rss-content", data: rssData };

// Vue 组件中
const payload = (window as any).utoolsPayload;
if (payload && payload.type === "rss-content") {
  rssData.value = payload.data;
}
```

## 🎯 学习要点

1. **Node.js 模块使用**：学习如何在预加载脚本中使用 Node.js 的 `fs`、`http`、`https` 等模块
2. **uTools API 集成**：了解如何使用 uTools 的通知、跳转、系统集成等功能
3. **数据解析**：学习简单的 XML 解析技术
4. **错误处理**：实现健壮的错误处理机制
5. **Vue 组件开发**：创建响应式的用户界面

这个示例提供了一个完整的 RSS 功能实现，既简单易懂，又具备实用性，是学习 uTools 插件开发的好例子！🎉
