# 路径别名配置指南

## 🎯 问题描述

当您在 TypeScript 项目中使用 `@/` 路径别名时，可能会遇到以下错误：

```
找不到模块"@/services/database/mainDataService"或其相应的类型声明。ts-plugin(2307)
```

## 🔧 解决方案

### 1. **配置 `vite.config.ts`**

确保 Vite 配置文件中包含路径别名设置：

```typescript
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
```

### 2. **配置 `tsconfig.json`**

这是关键步骤！在 TypeScript 配置文件中添加路径映射：

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "moduleResolution": "bundler",
    // ... 其他配置

    /* Path mapping - 这是关键配置 */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}
```

### 3. **确保类型文件正确导出**

如果您的类型定义文件（如 `main-data.ts`）包含接口，需要使用 `export` 关键字：

```typescript
// ❌ 错误：没有 export
interface MainDataDocument {
  // ...
}

// ✅ 正确：使用 export
export interface MainDataDocument {
  // ...
}
```

## 📁 **完整示例**

### **项目结构**

```
src/
├── types/
│   └── rss/
│       └── main-data.ts          # 类型定义文件
├── services/
│   └── database/
│       └── mainDataService.ts    # 服务文件
└── components/
    └── SomeComponent.vue         # 使用服务的组件
```

### **类型定义文件 (`src/types/rss/main-data.ts`)**

```typescript
export interface MainDataDocument {
  _id: "rss-browser/main-data";
  _rev?: string;
  children: Array<FolderNode | SubscriptionNode | ParagraphNode>;
  lastModifiedDate: string;
  version: string;
}

export interface FolderNode {
  type: "folder";
  id: string;
  name: string;
  // ... 其他属性
}

export interface SubscriptionNode {
  type: "subscription";
  id: string;
  url: string;
  // ... 其他属性
}
```

### **服务文件 (`src/services/database/mainDataService.ts`)**

```typescript
// 使用路径别名导入类型
import type { MainDataDocument } from "@/types/rss/main-data";

export async function getMainData(): Promise<MainDataDocument> {
  // 实现逻辑
}

export async function updateMainData(
  mainData: MainDataDocument
): Promise<MainDataDocument> {
  // 实现逻辑
}
```

### **组件中使用 (`src/components/SomeComponent.vue`)**

```vue
<script setup lang="ts">
// 使用路径别名导入服务
import {
  getMainData,
  updateMainData,
} from "@/services/database/mainDataService";

// 使用路径别名导入类型
import type { MainDataDocument } from "@/types/rss/main-data";

// 现在可以正常使用了
const loadData = async () => {
  const data = await getMainData();
  console.log(data);
};
</script>
```

## ⚠️ **常见问题**

### **问题 1：路径别名不生效**

- **原因**：`tsconfig.json` 中缺少 `baseUrl` 和 `paths` 配置
- **解决**：按照上面的配置添加路径映射

### **问题 2：类型声明找不到**

- **原因**：接口没有使用 `export` 关键字导出
- **解决**：在所有接口前添加 `export` 关键字

### **问题 3：IDE 仍然报错**

- **原因**：IDE 缓存问题
- **解决**：重启 IDE 或重新加载 TypeScript 服务

## 🎯 **验证配置**

配置完成后，运行以下命令验证：

```bash
# 类型检查
pnpm run build

# 或者只检查类型
npx vue-tsc --noEmit
```

如果没有错误输出，说明配置成功！

## 📋 **总结**

解决 `@/` 路径别名问题的关键步骤：

1. ✅ **Vite 配置**：添加 `resolve.alias` 配置
2. ✅ **TypeScript 配置**：添加 `baseUrl` 和 `paths` 配置
3. ✅ **类型导出**：确保接口使用 `export` 关键字
4. ✅ **重启 IDE**：清除缓存，重新加载配置

这样配置后，您就可以在整个项目中愉快地使用 `@/` 路径别名了！🎉
