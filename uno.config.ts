import {
  defineConfig,
  presetUno,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from "unocss";

export default defineConfig({
  // 预设
  presets: [
    presetUno(), // 默认预设，包含 Tailwind CSS 兼容的原子类
    presetAttributify(), // 属性化模式，支持将类名写成属性
    presetIcons({
      // 图标预设，支持各种图标库
      collections: {
        // 可以添加需要的图标集合
        // carbon: () => import('@iconify-json/carbon/icons.json').then(i => i.default),
        // mdi: () => import('@iconify-json/mdi/icons.json').then(i => i.default),
      },
    }),
  ],

  // 转换器
  transformers: [
    transformerDirectives(), // 支持 @apply 指令
    transformerVariantGroup(), // 支持变体组语法，如 hover:(bg-gray-400 font-medium)
  ],

  // 主题配置
  theme: {
    colors: {
      // 自定义颜色
      primary: {
        50: "#f0f9ff",
        100: "#e0f2fe",
        200: "#bae6fd",
        300: "#7dd3fc",
        400: "#38bdf8",
        500: "#0ea5e9",
        600: "#0284c7",
        700: "#0369a1",
        800: "#075985",
        900: "#0c4a6e",
      },
      pinia: {
        50: "#fefce8",
        100: "#fef9c3",
        200: "#fef08a",
        300: "#fde047",
        400: "#facc15",
        500: "#eab308",
        600: "#ca8a04",
        700: "#a16207",
        800: "#854d0e",
        900: "#713f12",
      },
      vue: {
        50: "#f0fdf4",
        100: "#dcfce7",
        200: "#bbf7d0",
        300: "#86efac",
        400: "#4ade80",
        500: "#22c55e",
        600: "#16a34a",
        700: "#15803d",
        800: "#166534",
        900: "#14532d",
      },
      // Obsidian 深色主题色彩
      obsidian: {
        bg: "#1e1e1e",
        sidebar: "#2d2d30",
        surface: "#383838",
        border: "#464647",
        text: "#e4e4e7",
        "text-muted": "#a1a1aa",
        accent: "#6366f1",
      },
      // 扩展灰色系
      gray: {
        750: "#374151",
        850: "#1f2937",
        950: "#0f172a",
      },
    },
    fontFamily: {
      sans: ["Inter", "ui-sans-serif", "system-ui", "sans-serif"],
      mono: ["Fira Code", "ui-monospace", "monospace"],
    },
    spacing: {
      "18": "4.5rem",
      "88": "22rem",
    },
    borderRadius: {
      "4xl": "2rem",
    },
  },

  // 快捷方式
  shortcuts: [
    // 按钮样式
    [
      "btn",
      "px-4 py-2 rounded inline-block bg-teal-600 text-white cursor-pointer hover:bg-teal-700 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50",
    ],
    ["btn-primary", "btn bg-primary-600 hover:bg-primary-700"],
    ["btn-secondary", "btn bg-gray-600 hover:bg-gray-700"],
    ["btn-success", "btn bg-vue-600 hover:bg-vue-700"],
    ["btn-warning", "btn bg-pinia-600 hover:bg-pinia-700"],
    ["btn-danger", "btn bg-red-600 hover:bg-red-700"],

    // 卡片样式
    ["card", "bg-white rounded-lg shadow-md p-6 border border-gray-200"],
    [
      "card-dark",
      "bg-gray-800 rounded-lg shadow-md p-6 border border-gray-700",
    ],

    // 输入框样式
    [
      "input",
      "px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",
    ],
    [
      "input-dark",
      "px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",
    ],

    // 布局样式
    ["flex-center", "flex items-center justify-center"],
    ["flex-between", "flex items-center justify-between"],
    ["container-center", "max-w-4xl mx-auto px-4"],

    // 文本样式
    [
      "text-gradient",
      "bg-gradient-to-r from-primary-600 to-vue-600 bg-clip-text text-transparent",
    ],
    ["heading", "text-2xl font-bold text-gray-900 dark:text-white"],
    ["subheading", "text-lg font-semibold text-gray-700 dark:text-gray-300"],
  ],

  // 规则
  rules: [
    // 自定义规则
    [/^m-(\d+)$/, ([, d]) => ({ margin: `${d}px` })],
    [/^p-(\d+)$/, ([, d]) => ({ padding: `${d}px` })],
  ],

  // 安全列表 - 确保这些类名不会被清除
  safelist: [
    "btn",
    "btn-primary",
    "btn-secondary",
    "card",
    "input",
    "flex-center",
    "text-gradient",
    "heading",
  ],

  // 内容检测
  content: {
    filesystem: ["src/**/*.{vue,js,ts,jsx,tsx}", "public/**/*.html"],
  },
});
