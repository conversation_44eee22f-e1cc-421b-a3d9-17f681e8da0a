<template>
  <SimpleModal title="添加 RSS" :show="show" @close="closeModal">
    <div class="add-rss-model">
      <div class="rss-page">
        <!-- RSS 订阅输入 -->
        <div class="rss-input-section obsidian-card">
          <div class="input-group">
            <input
              v-model="rssUrl"
              type="url"
              placeholder="输入 RSS 源地址，例如：https://example.com/rss"
              class="rss-input"
              @keyup.enter="subscribeRSS"
            />
            <button
              @click="subscribeRSS"
              :disabled="!rssUrl || loading"
              class="subscribe-btn"
            >
              {{ loading ? "订阅中..." : "订阅" }}
            </button>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading obsidian-card">
          <div class="loading-spinner"></div>
          <p class="obsidian-text-muted">正在获取 RSS 内容...</p>
        </div>

        <!-- 错误信息 -->
        <div v-if="error" class="error obsidian-card">
          <h3 class="error-title">❌ 订阅失败</h3>
          <p class="error-message">{{ error }}</p>
          <button @click="clearError" class="clear-error-btn">重试</button>
        </div>

        <!-- 示例 RSS 源 -->
        <div v-if="!error && !loading" class="rss-examples obsidian-card">
          <h3 class="obsidian-text">📚 示例 RSS 源</h3>
          <p class="obsidian-text-muted">您可以尝试以下 RSS 源：</p>
          <div class="example-list">
            <div
              v-for="example in exampleRSSFeeds"
              :key="example.url"
              class="example-item"
            >
              <div class="example-info">
                <strong class="obsidian-text">{{ example.name }}</strong>
                <span class="example-url obsidian-text-muted">{{
                  example.url
                }}</span>
              </div>
              <button @click="useExample(example.url)" class="use-example-btn">
                使用
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </SimpleModal>
</template>

<script lang="ts" setup>
import SimpleModal from "@/components/Models/SimpleModal/SimpleModal.vue";
import { ref } from "vue";
import { useRSSStore } from "@/stores/rssStore";

defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

// 定义组件可以触发的事件
const emit = defineEmits(["close"]);

// 使用rssStore
const rssStore = useRSSStore();

// 关闭模态框的方法
const closeModal = () => {
  emit("close");
};

// 响应式数据
const rssUrl = ref("");
const loading = ref(false);
const error = ref("");

// 示例 RSS 源
const exampleRSSFeeds = [
  {
    name: "阮一峰",
    url: "http://www.ruanyifeng.com/blog/atom.xml",
  },
  {
    name: "TechCrunch",
    url: "https://techcrunch.com/feed/",
  },
  {
    name: "GitHub Blog",
    url: "https://github.blog/feed/",
  },
];

// 订阅 RSS
const subscribeRSS = async () => {
  if (!rssUrl.value.trim()) return;

  loading.value = true;
  error.value = "";

  try {
    // 使用rssStore添加订阅源
    await rssStore.addSubscription(rssUrl.value);

    // 关闭模态框
    closeModal();
  } catch (err) {
    error.value =
      err instanceof Error ? err.message : "订阅失败，请检查 RSS 地址是否正确";
  } finally {
    loading.value = false;
  }
};

// 使用示例 RSS 源
const useExample = (url: string) => {
  rssUrl.value = url;
  subscribeRSS();
};

// 清除错误
const clearError = () => {
  error.value = "";
};
</script>

<style scoped src="./AddRssModel.css"></style>
