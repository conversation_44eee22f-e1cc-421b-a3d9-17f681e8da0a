# uTools TypeScript 类型支持

## 🎯 问题解决

当您在预加载脚本的模块文件中使用 `utools` 对象时，可能会遇到 TypeScript 错误：

```
找不到名称"utools"
```

## 🔧 解决方案

### 方案一：使用全局类型定义文件（推荐）

我们已经创建了 `public/preload/types/global.d.ts` 文件，包含完整的 uTools API 类型定义。

**使用方法**：
```typescript
// 在模块文件中直接使用，无需额外导入
export class MyModule {
  handleSomething() {
    utools.showNotification("Hello!"); // ✅ 类型安全
  }
}
```

### 方案二：在模块文件中声明类型

如果您不想使用全局类型文件，可以在每个模块文件顶部添加：

```typescript
// 声明全局 utools 对象类型
declare global {
  const utools: any;
}

export class MyModule {
  // 现在可以使用 utools 了
}
```

## 📋 可用的 uTools API

### 插件生命周期
```typescript
utools.onPluginEnter(({ code, type, payload }) => {
  // 插件进入处理
});

utools.onPluginOut(() => {
  // 插件退出处理
});
```

### 窗口操作
```typescript
utools.setExpendHeight(600);        // 设置窗口高度
utools.hideMainWindow();            // 隐藏窗口
utools.showMainWindow();            // 显示窗口
```

### 系统操作
```typescript
utools.shellOpenExternal(url);      // 打开外部链接
utools.shellOpenPath(path);         // 打开文件/文件夹
utools.shellShowItemInFolder(path); // 在文件夹中显示
```

### 通知和剪贴板
```typescript
utools.showNotification("消息");    // 显示通知
utools.copyText("文本内容");        // 复制文本到剪贴板
```

### 对话框
```typescript
const savePath = utools.showSaveDialog({
  title: "保存文件",
  defaultPath: "filename.txt",
  filters: [
    { name: "Text Files", extensions: ["txt"] },
    { name: "All Files", extensions: ["*"] }
  ]
});
```

### 路由跳转
```typescript
utools.redirect("page-code", { data: "some data" });
```

## 🏗️ 项目结构

```
public/preload/
├── services.ts              # 主预加载脚本
├── types/
│   └── global.d.ts         # 全局类型定义
├── modules/                # 功能模块（可选）
│   └── file-handler.ts     # 文件操作模块示例
├── package.json            # 依赖配置
└── tsconfig.json           # TypeScript 配置
```

## 🎨 最佳实践

### 1. 保持主文件简单
```typescript
// services.ts - 只处理基本生命周期
utools.onPluginEnter(({ code, type, payload }) => {
  // 简单的数据传递
  (window as any).utoolsPayload = { code, type, payload };
});
```

### 2. 复杂逻辑放在模块中
```typescript
// modules/file-handler.ts
export class FileHandler {
  handleRead(payload: any) {
    if (!payload) {
      utools.showNotification("请选择文件"); // ✅ 类型安全
      return;
    }
    // 处理文件读取逻辑
  }
}
```

### 3. 在主文件中组合模块
```typescript
// services.ts
import { FileHandler } from './modules/file-handler';

const fileHandler = new FileHandler();

utools.onPluginEnter(({ code, type, payload }) => {
  switch (code) {
    case 'read':
      fileHandler.handleRead(payload);
      break;
  }
});
```

## 🔍 调试技巧

### 1. 检查 uTools 环境
```typescript
if (typeof utools !== 'undefined') {
  console.log('运行在 uTools 环境');
} else {
  console.log('运行在浏览器环境');
}
```

### 2. 错误处理
```typescript
try {
  utools.showNotification("测试消息");
} catch (error) {
  console.error('uTools API 调用失败:', error);
}
```

### 3. 类型检查
```typescript
// 使用类型断言确保类型安全
const api = utools as UToolsApi;
api.showNotification("类型安全的调用");
```

现在您可以在预加载脚本的任何模块中安全地使用 uTools API 了！🎉
