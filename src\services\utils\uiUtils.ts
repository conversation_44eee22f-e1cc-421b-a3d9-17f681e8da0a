/**
 * UI 相关工具函数
 * 用于头像、颜色、标题等UI元素处理
 */

/**
 * 生成头像颜色
 * @param title 文章标题
 * @returns 头像颜色（十六进制颜色值）
 */
export const getAvatarColor = (title: string): string => {
  const colors = [
    "#ff6b35",
    "#00a8ff",
    "#2ed573",
    "#ffa502",
    "#ff4757",
    "#3742fa",
    "#ff6348",
  ];
  const index = title.length % colors.length;
  return colors[index];
};

/**
 * 获取标题首字符
 * @param title 文章标题
 * @returns 标题首字符（大写）
 */
export const getTitleInitial = (title: string): string => {
  return title.charAt(0).toUpperCase();
};
