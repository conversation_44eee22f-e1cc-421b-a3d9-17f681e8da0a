/**
 * 图片处理相关工具函数
 * 用于图片提取、处理和错误处理
 */

/**
 * 从HTML内容中提取第一张图片
 * @param content HTML内容
 * @returns 图片URL或空字符串
 */
export function extractFirstImageFromContent(
  content: string | undefined
): string {
  if (!content) {
    return "";
  }
  // 使用正则表达式匹配img标签的src属性
  const imgRegex = /<img[^>]+src="([^"]+)"/i;
  const match = content.match(imgRegex);
  return match ? match[1] : "";
}

/**
 * 处理图片加载错误
 * @param event 图片加载错误事件
 */
export const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.style.display = "none";
};
