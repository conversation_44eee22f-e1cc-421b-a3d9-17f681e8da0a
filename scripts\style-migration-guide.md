# UnoCSS 样式迁移指南

## 🎯 迁移目标

将项目从传统 CSS 迁移到 UnoCSS 原子类系统，提高样式的可维护性和一致性。

## 📁 新的样式结构（混合方案）

### 方案说明

我们采用了**混合方案**来组织样式文件：

- **组件样式**：与组件就近放置（推荐）
- **页面样式**：集中管理在 styles/pages/
- **全局样式**：集中管理在 styles/

```
src/
├── components/
│   ├── Layout/
│   │   ├── Layout.vue        # 布局组件
│   │   ├── Layout.css        # 布局样式（就近原则）
│   │   └── index.ts          # 导出文件
│   └── Navigation/
│       ├── Navigation.vue    # 导航组件
│       ├── Navigation.css    # 导航样式（就近原则）
│       └── index.ts          # 导出文件
└── styles/
    ├── index.css             # 主样式文件
    ├── global.css            # 全局样式和重置
    ├── components/           # 集中管理的组件样式
    │   ├── hello.css        # Hello 组件样式
    │   └── pinia.css        # Pinia 页面样式
    └── pages/               # 页面样式
        ├── home.css         # Home 页面样式
        └── read-write.css   # Read/Write 页面样式
```

### 组织原则

1. **复杂组件**：使用目录结构，样式就近放置
2. **简单组件**：样式可以集中管理
3. **页面样式**：统一放在 styles/pages/
4. **全局样式**：统一放在 styles/

## 🔧 UnoCSS 配置特性

### 自定义颜色主题

- `primary-*`: 主色调 (蓝色系)
- `vue-*`: Vue 绿色系
- `pinia-*`: Pinia 黄色系

### 快捷方式 (Shortcuts)

- `btn`: 基础按钮样式
- `btn-primary`, `btn-secondary`, `btn-success`, `btn-warning`, `btn-danger`: 不同类型按钮
- `card`, `card-dark`: 卡片样式
- `input`, `input-dark`: 输入框样式
- `flex-center`, `flex-between`: 布局快捷方式
- `container-center`: 居中容器
- `heading`, `subheading`: 标题样式
- `text-gradient`: 渐变文字

## 📝 常用类名映射

### 布局

```css
/* 旧样式 → UnoCSS */
display: flex → flex
justify-content: center → justify-center
align-items: center → items-center
flex-direction: column → flex-col
gap: 10px → gap-2.5
```

### 间距

```css
/* 旧样式 → UnoCSS */
margin: 20px → m-5
padding: 16px → p-4
margin-top: 8px → mt-2
padding-left: 12px → pl-3
```

### 颜色

```css
/* 旧样式 → UnoCSS */
color: #42b883 → text-vue-500
background: #f9f9f9 → bg-gray-50
border-color: #42b883 → border-vue-500
```

### 尺寸

```css
/* 旧样式 → UnoCSS */
width: 100% → w-full
max-width: 800px → max-w-4xl
height: auto → h-auto
min-height: 100vh → min-h-screen
```

### 字体

```css
/* 旧样式 → UnoCSS */
font-size: 14px → text-sm
font-weight: bold → font-bold
text-align: center → text-center
font-family: monospace → font-mono
```

### 边框和圆角

```css
/* 旧样式 → UnoCSS */
border: 1px solid #ddd → border border-gray-300
border-radius: 8px → rounded-lg
border-left: 4px solid #42b883 → border-l-4 border-vue-500
```

### 阴影和效果

```css
/* 旧样式 → UnoCSS */
box-shadow: 0 2px 4px rgba(0,0,0,0.1) → shadow-md
transition: all 0.2s → transition-all duration-200
transform: translateY(-1px) → transform -translate-y-0.5
```

## 🎨 响应式设计

UnoCSS 支持响应式前缀：

- `sm:` - 640px 及以上
- `md:` - 768px 及以上
- `lg:` - 1024px 及以上
- `xl:` - 1280px 及以上

示例：

```html
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3"></div>
```

## 🌙 深色模式

使用 `dark:` 前缀：

```html
<div class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white"></div>
```

## 📋 迁移检查清单

### ✅ 已完成

- [x] 安装和配置 UnoCSS
- [x] 创建样式文件结构
- [x] 重构 Hello 组件
- [x] 提取组件样式到独立文件
- [x] 配置自定义主题和快捷方式

### 🔄 待完成

- [ ] 重构 Layout 组件
- [ ] 重构 Navigation 组件
- [ ] 重构 Home 页面
- [ ] 重构 Read 页面
- [ ] 重构 Write 页面
- [ ] 重构 Pinia 页面
- [ ] 优化样式文件大小
- [ ] 添加更多自定义组件样式

## 🚀 使用建议

1. **优先使用快捷方式**: 使用预定义的 `btn`、`card` 等快捷方式
2. **保持一致性**: 使用统一的颜色主题和间距系统
3. **响应式优先**: 考虑移动端适配
4. **语义化类名**: 在必要时使用 CSS 文件中的语义化类名
5. **性能优化**: 避免不必要的样式重复

## 🔍 调试工具

- UnoCSS Inspector: http://localhost:5173/\_\_unocss/
- 可以查看生成的 CSS 和使用的类名
- 实时预览样式效果
